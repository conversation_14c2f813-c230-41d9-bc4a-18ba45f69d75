import { ApiErrorResponse } from "@/types/api-types";
import { NextResponse } from "next/server";

export function genericErrorHandler(
  e: unknown
): NextResponse<ApiErrorResponse> {
  console.error(e);
  if (e instanceof Error) {
    return NextResponse.json({ errors: [e.message] }, { status: 500 });
  } else if (!!e) {
    return NextResponse.json({ errors: [JSON.stringify(e)] }, { status: 500 });
  }
  return NextResponse.json({ errors: ["Unknown error"] }, { status: 500 });
}
