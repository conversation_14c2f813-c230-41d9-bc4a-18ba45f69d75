import NextAuth from "next-auth";
import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import EmailProvider from "next-auth/providers/email";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
import config from "@/config";
import connectMongo from "./mongo";
import env from "@/libs/env";
import { DISCORD_WEBHOOK } from "./discord";
import { createDefaultCollectionForUser } from "@/lib/server-collection-utils";

interface NextAuthOptionsExtended extends NextAuthOptions {
  adapter?: any;
}

export const authOptions: NextAuthOptionsExtended = {
  // Set any random key in .env.local
  secret: env.NEXTAUTH_SECRET,
  providers: [
    GoogleProvider({
      // Follow the "Login with Google" tutorial to get your credentials
      clientId: env.GOOGLE_ID,
      clientSecret: env.GOOGLE_SECRET,
      allowDangerousEmailAccountLinking: true,
      async profile(profile) {
        return {
          id: profile.sub,
          name: profile.given_name ? profile.given_name : profile.name,
          email: profile.email,
          image: profile.picture,
          createdAt: new Date(),
        };
      },
    }),
    // Follow the "Login with Email" tutorial to set up your email server
    // Requires a MongoDB database. Set MONOGODB_URI env variable.
    ...(connectMongo
      ? [
          EmailProvider({
            server: {
              host: "smtp.resend.com",
              port: 465,
              auth: {
                user: "resend",
                pass: process.env.RESEND_API_KEY,
              },
            },
            from: config.resend.fromNoReply,
          }),
        ]
      : []),
  ],
  // New users will be saved in Database (MongoDB Atlas). Each user (model) has some fields like name, email, image, etc..
  // Requires a MongoDB database. Set MONOGODB_URI env variable.
  // Learn more about the model type: https://next-auth.js.org/v3/adapters/models
  ...(connectMongo && { adapter: MongoDBAdapter(connectMongo) }),

  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user) {
        session.user.id = token.sub || "N/A";
      }
      return session;
    },
    signIn: async ({ user, account, profile, email, credentials }) => {
      // Send Discord notification when user logs in
      if (user?.email) {
        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "🔑 User Login",
              description: `A user has logged in to ${config.metadata.appName}`,
              color: 0x00ff00, // Green color
              timestamp: new Date().toISOString(),
              fields: [
                {
                  name: "Email",
                  value: `||${user.email}||`,
                  inline: true,
                },
                {
                  name: "Name",
                  value: user.name || "N/A",
                  inline: true,
                },
                {
                  name: "Provider",
                  value: account?.provider || "N/A",
                  inline: true,
                },
              ],
              footer: {
                text: config.metadata.appName,
                icon_url: "https://dothistask.ai/logo.png",
              },
            },
          ],
        });
      }
      return true;
    },
  },
  events: {
    createUser: async ({ user }) => {
      try {
        // Create default collection for new user
        if (user.id) {
          await createDefaultCollectionForUser(user.id);

          // Send Discord notification for new user creation
          await DISCORD_WEBHOOK.send({
            embeds: [
              {
                title: "👋 New User Created",
                color: 0x0099ff, // Blue color
                timestamp: new Date().toISOString(),
                fields: [
                  {
                    name: "Email",
                    value: `||${user.email}||`,
                    inline: true,
                  },
                  {
                    name: "Name",
                    value: user.name || "N/A",
                    inline: true,
                  },
                  {
                    name: "User ID",
                    value: user.id,
                    inline: true,
                  },
                ],
                footer: {
                  text: config.metadata.appName,
                  icon_url: "https://dothistask.ai/logo.png",
                },
              },
            ],
          });
        }
      } catch (error) {
        console.error("Error in createUser event:", error);
        // Don't throw error to prevent disrupting user creation
      }
    },
  },
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/signin",
  },
  theme: {
    brandColor: config.colors.main,
    // Add you own logo below. Recommended size is rectangle (i.e. 200x50px) and show your logo + name.
    // It will be used in the login flow to display your logo. If you don't add it, it will look faded.
    logo: `https://${config.metadata.domainName}/logoAndName.png`,
  },
};

export default NextAuth(authOptions);
