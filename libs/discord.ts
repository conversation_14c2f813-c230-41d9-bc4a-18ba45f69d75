import config from "@/config";
import env from "./env";

type WebhookContent = {
  content?: string;
  embeds: {
    title: string;
    description?: string;
    url?: string;
    color?: number;
    timestamp?: string;
    footer?: {
      text: string;
      icon_url?: string;
    };
    thumbnail?: {
      url: string;
    };
    image?: {
      url: string;
    };
    author?: {
      name: string;
      url?: string;
      icon_url?: string;
    };
    fields?: {
      name: string;
      value: string;
      inline?: boolean;
    }[];
  }[];
};

// I may want to replace this with a more robust library if I have the need for more complex features (ex: embeds, file uploading)
class DiscordWebhook {
  private url: string;
  private username: string;
  private avatar: string;

  constructor(url: string) {
    this.url = url;
    this.username = `${config.metadata.appName} - ${env.STAGE === "development" ? "(local)" : ""}`;
    this.avatar = `https://${config.metadata.domainName}/icon.png`;
  }

  async send(content: string | WebhookContent) {
    if (!this.url) return;

    try {
      const payload =
        typeof content === "string"
          ? {
              username: this.username,
              avatar_url: this.avatar,
              content,
            }
          : {
              username: this.username,
              avatar_url: this.avatar,
              content: content.content,
              embeds: content.embeds,
            };

      await fetch(this.url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      console.error("Discord webhook error:", error);
    }
  }
}

export const DISCORD_WEBHOOK = new DiscordWebhook(env.DISCORD_WEBHOOK_URL);
export const DISCORD_SAMPLES_WEBHOOK = new DiscordWebhook(
  env.DISCORD_SAMPLES_WEBHOOK_URL
);
