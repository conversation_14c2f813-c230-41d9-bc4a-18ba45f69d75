type Plan = {
  name: string;
  features: string[];
  stripePriceId?: string;
};

export const PLANS = {
  free: {
    name: "Free",
    features: ["3 directory solutions per day", "8 agent tokens per month"],
  },
  pro: {
    name: "Premium",
    features: [
      "Full directory access",
      "50 agent tokens per month",
      "Priority support",
    ],
    stripePriceId:
      process.env.NODE_ENV === "development"
        ? "price_1RimN1CsNAcMWoALOoK4ZQiQ"
        : "price_1RjTh3ECvMxpPM59cpkVQtMA",
  },
} as const satisfies Record<string, Plan>;

export type PlanId = keyof typeof PLANS;
export type PlanData = (typeof PLANS)[PlanId];
