import OpenA<PERSON> from "openai";
import { <PERSON>con<PERSON> } from "@pinecone-database/pinecone";
import env from "@/libs/env";
import { PINECONE_CONFIG, PineconeMetadata } from "@/libs/pinecone";
import {
  SearchTasksInput,
  SearchResult,
  SearchResponse,
} from "@/types/models/search";

/**
 * Initialize OpenAI client for embeddings
 */
function initializeOpenAI() {
  return new OpenAI({
    apiKey: env.OPENAI_API_KEY,
  });
}

/**
 * Initialize Pinecone client and get index
 */
async function initializePinecone() {
  const pc = new Pinecone({ apiKey: env.PINECONE_API_KEY });
  const index = pc.index(env.PINECONE_INDEX_NAME);
  return { pc, index };
}

/**
 * Generate embedding for search query using OpenAI
 */
async function generateQueryEmbedding(query: string): Promise<number[]> {
  const openai = initializeOpenAI();

  try {
    const response = await openai.embeddings.create({
      input: query,
      model: PINECONE_CONFIG.EMBEDDING_MODEL,
    });

    const embedding = response.data[0]?.embedding;
    if (!embedding) {
      throw new Error("No embedding generated for query");
    }

    return embedding;
  } catch (error) {
    throw new Error(`OpenAI embedding generation failed: ${error}`);
  }
}

/**
 * Search Pinecone for similar task vectors
 */
async function searchPineconeVectors({
  query,
  limit,
  cursor,
  minScore,
  roleFilter,
}: SearchTasksInput): Promise<SearchResponse> {
  const startTime = Date.now();

  try {
    // Generate embedding for the search query
    const queryEmbedding = await generateQueryEmbedding(query);

    // Initialize Pinecone
    const { index } = await initializePinecone();

    // Build filter for role-based filtering
    let filter: Record<string, any> | undefined;
    if (roleFilter && roleFilter.length > 0) {
      filter = {
        roleIds: { $in: roleFilter },
      };
    }

    // Perform vector search - get more results for pagination
    const searchResponse = await index
      .namespace(PINECONE_CONFIG.NAMESPACE)
      .query({
        vector: queryEmbedding,
        topK: Math.min(limit * 10, 1000), // Get extra results for cursor-based pagination
        includeMetadata: true,
        filter,
      });

    // Process results and filter by minimum score
    const allResults: SearchResult[] = [];

    if (searchResponse.matches) {
      for (const match of searchResponse.matches) {
        // Filter by minimum score
        if (match.score && match.score >= minScore) {
          allResults.push({
            id: match.id,
            score: match.score,
            metadata: match.metadata as PineconeMetadata,
          });
        }
      }
    }

    // Handle cursor-based pagination for search results
    let filteredResults = allResults;

    // If we have a cursor, filter results to only include those after the cursor
    if (cursor) {
      const cursorIndex = allResults.findIndex(
        (result) => result.metadata.taskId === cursor
      );
      if (cursorIndex !== -1) {
        filteredResults = allResults.slice(cursorIndex + 1);
      }
    }

    // Apply limit and determine if there are more results
    const results = filteredResults.slice(0, limit);
    const hasNextPage = filteredResults.length > limit;
    const nextCursor = hasNextPage
      ? results[results.length - 1]?.metadata.taskId
      : undefined;

    const processingTimeMs = Date.now() - startTime;

    return {
      results,
      nextCursor,
      hasNextPage,
      query,
      processingTimeMs,
      totalCount: allResults.length, // Total results that meet minimum score threshold
    };
  } catch (error) {
    throw new Error(`Pinecone search failed: ${error}`);
  }
}

/**
 * Main search function that orchestrates the entire search process
 */
export async function searchTasks(
  input: SearchTasksInput
): Promise<SearchResponse> {
  try {
    // Validate input
    if (!input.query.trim()) {
      throw new Error("Search query cannot be empty");
    }

    // Perform the search
    const results = await searchPineconeVectors(input);

    return results;
  } catch (error) {
    console.error("Task search failed:", error);
    throw error;
  }
}

/**
 * Health check function to verify search infrastructure
 */
export async function checkSearchHealth(): Promise<{
  pinecone: boolean;
  openai: boolean;
  errors: string[];
}> {
  const errors: string[] = [];
  let pineconeHealthy = false;
  let openaiHealthy = false;

  // Test Pinecone connection
  try {
    const { index } = await initializePinecone();
    await index.describeIndexStats();
    pineconeHealthy = true;
  } catch (error) {
    errors.push(`Pinecone: ${error}`);
  }

  // Test OpenAI connection
  try {
    const openai = initializeOpenAI();
    await openai.embeddings.create({
      input: "test",
      model: PINECONE_CONFIG.EMBEDDING_MODEL,
    });
    openaiHealthy = true;
  } catch (error) {
    errors.push(`OpenAI: ${error}`);
  }

  return {
    pinecone: pineconeHealthy,
    openai: openaiHealthy,
    errors,
  };
}
