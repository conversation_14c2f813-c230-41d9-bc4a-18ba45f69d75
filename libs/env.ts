import { z } from "zod";

const BOOLEAN_STRING_MAP: Record<string, boolean> = {
  true: true,
  false: false,
};

const stringToBooleanZodTranformer = z.string().transform((val) => {
  if (!(val in BOOLEAN_STRING_MAP)) {
    throw new Error("Invalid boolean string");
  }
  return BOOLEAN_STRING_MAP[val];
});

const featuresSchema = z.object({
  BLOG_ENABLED_FLAG: stringToBooleanZodTranformer.default("false"),
  WAITLIST_MODE_FLAG: stringToBooleanZodTranformer.default("true"), // set to false in env to show normal landing page instead of waitlist
});

const envSchema = z
  .object({
    RESEND_API_KEY: z.string(),
    STRIPE_SECRET_KEY: z.string(),
    STRIPE_WEBHOOK_SECRET: z.string(),
    NEXTAUTH_SECRET: z.string(),
    GOOGLE_ID: z.string(),
    GOOGLE_SECRET: z.string(),
    UPSTASH_REDIS_REST_URL: z.string(),
    UPSTASH_REDIS_REST_TOKEN: z.string(),
    DISCORD_WEBHOOK_URL: z.string(),
    DISCORD_SAMPLES_WEBHOOK_URL: z.string(),
    OPENAI_API_KEY: z.string(),
    PINECONE_API_KEY: z.string(),
    PINECONE_INDEX_NAME: z.string(),
    STAGE: z.enum(["development", "production"]).default("production"),
  })
  .merge(featuresSchema);

const env = envSchema.parse(process.env);

export default env;
