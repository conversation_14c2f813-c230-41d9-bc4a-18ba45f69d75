import { z } from "zod";

/**
 * Pinecone metadata schema for task records
 * Defines the structure of metadata stored with each task vector in Pinecone
 */
export const PineconeMetadataSchema = z
  .object({
    taskId: z.string(),
    taskName: z.string(),
    taskDescription: z.string(),
    roleIds: z.array(z.string()),
    roleNames: z.array(z.string()),
    lastSyncedAt: z.number(),
    taskUpdatedAt: z.number(),
    taskCreatedAt: z.number(),
    roleCount: z.number(),
    hasMultipleRoles: z.boolean(),
  })
  .catchall(z.any()); // Allow additional properties for Pinecone compatibility

/**
 * TypeScript type inferred from the zod schema
 */
export type PineconeMetadata = z.infer<typeof PineconeMetadataSchema>;

/**
 * Pinecone record schema for task vectors
 * Represents a complete record that gets stored in Pinecone
 */
export const PineconeRecordSchema = z.object({
  id: z.string(),
  values: z.array(z.number()),
  metadata: PineconeMetadataSchema,
});

/**
 * TypeScript type inferred from the zod schema
 */
export type PineconeRecord = z.infer<typeof PineconeRecordSchema>;

/**
 * Pinecone configuration constants
 */
export const PINECONE_CONFIG = {
  NAMESPACE: "tasks",
  BATCH_SIZE: 100,
  EMBEDDING_MODEL: "text-embedding-3-small",
  EMBEDDING_DIMENSIONS: 1536,
  EMBEDDING_BATCH_SIZE: 1000,
} as const;

/**
 * Creates a safe Pinecone metadata object with validation
 * @param params - Parameters for creating metadata
 * @returns Validated PineconeMetadata
 */
export function createPineconeMetadata(params: {
  taskId: string;
  taskName: string;
  taskDescription: string;
  roleIds: string[];
  roleNames: string[];
  lastSyncedAt: number;
  taskUpdatedAt: number;
  taskCreatedAt: number;
}): PineconeMetadata {
  const metadata = {
    ...params,
    roleCount: params.roleIds.length,
    hasMultipleRoles: params.roleIds.length > 1,
  };

  return PineconeMetadataSchema.parse(metadata);
}

/**
 * Creates a safe Pinecone record object with validation
 * @param params - Parameters for creating a record
 * @returns Validated PineconeRecord
 */
export function createPineconeRecord(params: {
  id: string;
  values: number[];
  metadata: PineconeMetadata;
}): PineconeRecord {
  return PineconeRecordSchema.parse(params);
}
