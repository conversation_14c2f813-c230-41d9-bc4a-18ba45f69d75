import type { Metadata } from "next";
import config from "@/config";

// These are all the SEO tags you can add to your pages.
// It prefills data with default title/description/OG, etc.. and you can cusotmize it for each page.
// It's already added in the root layout.js so you don't have to add it to every pages
// But I recommend to set the canonical URL for each page (export const metadata = getSEOTags({canonicalUrlRelative: "/"});)
// See https://shipfa.st/docs/features/seo

// Enhanced default keywords for DoThisTaskAI
const DEFAULT_KEYWORDS = [
  config.metadata.appName,
  "AI automation",
  "process identification",
  "knowledge workers",
  "AI solutions",
  "task automation",
  "productivity tools",
  "business process automation",
  "workflow automation",
  "AI-powered solutions",
];

export const getSEOTags = ({
  title,
  description,
  keywords,
  openGraph,
  canonicalUrlRelative,
  extraTags,
}: Metadata & {
  canonicalUrlRelative?: string;
  extraTags?: Record<string, any>;
} = {}) => {
  return {
    // up to 50 characters (what does your app do for the user?) > your main should be here
    title: title || config.metadata.appName,
    // up to 160 characters (how does your app help the user?)
    description: description || config.metadata.appDescription,
    // Enhanced keywords that reflect the platform's purpose
    keywords: keywords || DEFAULT_KEYWORDS,
    applicationName: config.metadata.appName,
    // set a base URL prefix for other fields that require a fully qualified URL (.e.g og:image: og:image: 'https://yourdomain.com/share.png' => '/share.png')
    metadataBase: new URL(
      process.env.NODE_ENV === "development"
        ? "http://localhost:3000/"
        : `https://${config.metadata.domainName}/`
    ),

    openGraph: {
      title: openGraph?.title || config.metadata.appName,
      description: openGraph?.description || config.metadata.appDescription,
      url: openGraph?.url || `https://${config.metadata.domainName}/`,
      siteName: openGraph?.title || config.metadata.appName,
      // If you add an opengraph-image.(jpg|jpeg|png|gif) image to the /app folder, you don't need the code below
      // images: [
      //   {
      //     url: `https://${config.metadata.domainName}/share.png`,
      //     width: 1200,
      //     height: 660,
      //   },
      // ],
      locale: "en_US",
      type: "website",
    },

    twitter: {
      title: openGraph?.title || config.metadata.appName,
      description: openGraph?.description || config.metadata.appDescription,
      // If you add an twitter-image.(jpg|jpeg|png|gif) image to the /app folder, you don't need the code below
      // images: [openGraph?.image || defaults.og.image],
      card: "summary_large_image",
      creator: config.metadata.creator.twitterHandle,
    },

    // If a canonical URL is given, we add it. The metadataBase will turn the relative URL into a fully qualified URL
    ...(canonicalUrlRelative && {
      alternates: { canonical: canonicalUrlRelative },
    }),

    // If you want to add extra tags, you can pass them here
    ...extraTags,
  };
};

// Enhanced Structured Data for DoThisTaskAI with freemium business model
// Find your type here (SoftwareApp, Book...): https://developers.google.com/search/docs/appearance/structured-data/search-gallery
// Use this tool to check data is well structure: https://search.google.com/test/rich-results
export const renderSchemaTags = () => {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          "@context": "http://schema.org",
          "@type": "SoftwareApplication",
          name: config.metadata.appName,
          description: config.metadata.appDescription,
          image: `https://${config.metadata.domainName}/icon.png`,
          url: `https://${config.metadata.domainName}/`,
          author: {
            "@type": "Person",
            name: config.metadata.creator.name,
          },
          publisher: {
            "@type": "Organization",
            name: config.metadata.appName,
            url: `https://${config.metadata.domainName}/`,
          },
          datePublished: "2025-07-01",
          applicationCategory: "BusinessApplication",
          applicationSubCategory: "Process Automation",
          operatingSystem: "Web Browser",
          audience: {
            "@type": "Audience",
            audienceType: "Knowledge Workers",
            geographicArea: "Global",
          },
          featureList: [
            "AI-powered process identification",
            "Custom solution generation",
            "Task automation workflows",
            "Role-specific solutions",
            "Business process optimization",
          ],
          aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: "4.8",
            ratingCount: "12",
          },
        }),
      }}
    ></script>
  );
};

// SEO utility functions for specific entity types
import type { RoleItem } from "@/types/models/role";
import type { SolutionItem } from "@/types/models/solution";

/**
 * Generate SEO metadata for role pages with enhanced keywords
 */
export function getRoleSEOTags(role: RoleItem, roleSlug: string) {
  const title = `${role.roleName} AI Solutions | ${config.metadata.appName}`;
  const description =
    role.roleDescription.length > 160
      ? `${role.roleDescription.substring(0, 157)}...`
      : role.roleDescription;

  const roleKeywords = [
    role.roleName,
    `${role.roleName} automation`,
    `${role.roleName} AI tools`,
    `${role.roleName} productivity`,
    config.metadata.appName,
    "process automation",
    "task identification",
    "workflow optimization",
    "AI solutions",
    "knowledge worker tools",
  ];

  return getSEOTags({
    title,
    description,
    keywords: roleKeywords,
    canonicalUrlRelative: `/roles/${roleSlug}`,
    openGraph: {
      title,
      description,
      url: `/roles/${roleSlug}`,
      type: "website",
    },
  });
}

/**
 * Generate SEO metadata for solution pages with automation focus
 */
export function getSolutionSEOTags(solution: SolutionItem) {
  const title = `${solution.name} - AI Solution | ${config.metadata.appName}`;
  const description =
    solution.description.length > 160
      ? `${solution.description.substring(0, 157)}...`
      : solution.description;

  const solutionKeywords = [
    solution.name,
    "AI solution",
    "process automation",
    "task automation",
    "workflow solution",
    config.metadata.appName,
    "business automation",
    "productivity enhancement",
    "AI-powered tools",
  ];

  return getSEOTags({
    title,
    description,
    keywords: solutionKeywords,
    canonicalUrlRelative: `/solutions/${solution.id}`,
    openGraph: {
      title,
      description,
      url: `/solutions/${solution.id}`,
      type: "website",
    },
  });
}

/**
 * Generate SEO metadata for role-specific solution pages
 */
export function getRoleSolutionSEOTags({
  role,
  solution,
  roleSlug,
}: {
  role: RoleItem;
  solution: SolutionItem;
  roleSlug: string;
}) {
  const title = `${solution.name} for ${role.roleName} | ${config.metadata.appName}`;
  const description = `${solution.description} Specifically designed to automate ${role.roleName} workflows and boost productivity.`;
  const finalDescription =
    description.length > 160
      ? `${description.substring(0, 157)}...`
      : description;

  const combinedKeywords = [
    solution.name,
    role.roleName,
    `${role.roleName} automation`,
    `${solution.name} for ${role.roleName}`,
    config.metadata.appName,
    "role-specific AI",
    "targeted automation",
    "custom solutions",
    "workflow optimization",
  ];

  return getSEOTags({
    title,
    description: finalDescription,
    keywords: combinedKeywords,
    canonicalUrlRelative: `/roles/${roleSlug}/solutions/${solution.id}`,
    openGraph: {
      title,
      description: finalDescription,
      url: `/roles/${roleSlug}/solutions/${solution.id}`,
      type: "website",
    },
  });
}

/**
 * Generate enhanced structured data for role pages
 */
export function getRoleStructuredData(role: RoleItem, roleSlug: string) {
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: `${role.roleName} AI Solutions`,
    description: role.roleDescription,
    url: `https://${config.metadata.domainName}/roles/${roleSlug}`,
    mainEntity: {
      "@type": "ItemList",
      name: `${role.roleName} Automation Tasks`,
      description: `AI-powered automation solutions and process identification for ${role.roleName} professionals`,
      audience: {
        "@type": "Audience",
        audienceType: role.roleName,
        description: `${role.roleName} looking to automate repetitive tasks and optimize workflows`,
      },
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: `https://${config.metadata.domainName}`,
        },
        {
          "@type": "ListItem",
          position: 2,
          name: `${role.roleName} Solutions`,
          item: `https://${config.metadata.domainName}/roles/${roleSlug}`,
        },
      ],
    },
    about: {
      "@type": "Thing",
      name: "Process Automation",
      description:
        "AI-powered tools for identifying and automating business processes",
    },
  };
}

/**
 * Generate enhanced structured data for solution pages
 */
export function getSolutionStructuredData(solution: SolutionItem) {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: solution.name,
    description: solution.description,
    url: `https://${config.metadata.domainName}/solutions/${solution.id}`,
    applicationCategory: "BusinessApplication",
    applicationSubCategory: "Process Automation",
    operatingSystem: "Web Browser",
    author: {
      "@type": "Organization",
      name: config.metadata.appName,
      description:
        "Platform for connecting knowledge workers with AI automation solutions",
    },
    audience: {
      "@type": "Audience",
      audienceType: "Knowledge Workers",
      description:
        "Professionals looking to automate repetitive tasks and optimize workflows",
    },
    featureList: [
      "AI-powered automation",
      "Process identification",
      "Workflow optimization",
      "Custom solution generation",
    ],
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: `https://${config.metadata.domainName}`,
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Solutions",
          item: `https://${config.metadata.domainName}/`,
        },
        {
          "@type": "ListItem",
          position: 3,
          name: solution.name,
          item: `https://${config.metadata.domainName}/solutions/${solution.id}`,
        },
      ],
    },
  };
}

/**
 * Generate enhanced structured data for role-solution pages
 */
export function getRoleSolutionStructuredData({
  role,
  solution,
  roleSlug,
}: {
  role: RoleItem;
  solution: SolutionItem;
  roleSlug: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: `${solution.name} for ${role.roleName}`,
    description: `${solution.description} Specifically tailored for ${role.roleName} professionals to automate workflows and boost productivity.`,
    url: `https://${config.metadata.domainName}/roles/${roleSlug}/solutions/${solution.id}`,
    mainEntity: {
      "@type": "SoftwareApplication",
      name: solution.name,
      description: solution.description,
      applicationCategory: "BusinessApplication",
      applicationSubCategory: "Role-specific Automation",
      audience: {
        "@type": "Audience",
        audienceType: role.roleName,
        description: `${role.roleName} seeking to automate specific tasks and processes`,
      },
      featureList: [
        `${role.roleName}-specific automation`,
        "Custom workflow optimization",
        "AI-powered process identification",
        "Tailored solution generation",
      ],
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: `https://${config.metadata.domainName}`,
        },
        {
          "@type": "ListItem",
          position: 2,
          name: role.roleName,
          item: `https://${config.metadata.domainName}/roles/${roleSlug}`,
        },
        {
          "@type": "ListItem",
          position: 3,
          name: solution.name,
          item: `https://${config.metadata.domainName}/roles/${roleSlug}/solutions/${solution.id}`,
        },
      ],
    },
    about: [
      {
        "@type": "Thing",
        name: "Process Automation",
        description: "Automated solutions for business process optimization",
      },
      {
        "@type": "Thing",
        name: role.roleName,
        description: `Professional role focused on ${role.roleDescription}`,
      },
    ],
  };
}

/**
 * Generate organization structured data for DoThisTaskAI
 */
export function getOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: config.metadata.appName,
    description: config.metadata.appDescription,
    url: `https://${config.metadata.domainName}`,
    logo: `https://${config.metadata.domainName}/icon.png`,
    founder: {
      "@type": "Person",
      name: config.metadata.creator.name,
      description: config.metadata.creator.description,
    },
    knowsAbout: [
      "Process Automation",
      "AI Solutions",
      "Knowledge Worker Productivity",
      "Business Process Optimization",
      "Workflow Automation",
    ],
    areaServed: "Global",
    serviceType: "Software as a Service",
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "AI Automation Solutions",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Process Identification",
            description:
              "AI-powered identification of automation opportunities",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Solution Generation",
            description:
              "Custom AI solution creation for specific tasks and roles",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Directory Module",
            description:
              "Curated database of role-specific automation solutions",
          },
        },
      ],
    },
  };
}
