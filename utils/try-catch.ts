type Success<TData> = {
  data: TData;
  error: null;
};

type Failure<TFailure> = {
  data: null;
  error: TFailure;
};

type Result<TData, TFailure = Error> = Success<TData> | Failure<TFailure>;

/**
 * Wraps a promise in a try-catch block and returns a standardized Result object.
 * This utility function provides a consistent error handling pattern for async operations.
 *
 * @template TData The type of data returned by the promise on success.
 * @template TFailure The type of error returned on failure, defaults to Error.
 * @param {Promise<TData>} promise The promise to be executed.
 * @returns {Promise<Result<TData, TFailure>>} A promise that resolves to a Result object.
 *
 * @example
 * // Example usage:
 * const result = await tryCatch(fetchUserData(userId));
 * if (result.error) {
 *   // Handle error
 *   console.error(result.error);
 *   return {
 *     status: 500,
 *     body: { error: result.error.message },
 *   };
 * }
 * // Use the data
 * const userData = result.data;
 */
export async function tryCatch<TData, TFailure = Error>(
  promise: Promise<TData>
): Promise<Result<TData, TFailure>> {
  try {
    const data = await promise;
    return {
      data,
      error: null,
    };
  } catch (error) {
    return {
      data: null,
      error: error as TFailure,
    };
  }
}
