import { DISCORD_WEBHOOK } from "@/libs/discord";

export interface PromptUsageNotificationData {
  solutionId: string;
  solutionName: string;
  usageCount: number;
  userEmail?: string;
  userIp: string;
  userAgent?: string;
}

export interface SearchNotificationData {
  query: string;
  resultsFound: number;
  totalCount: number;
  hasNextPage: boolean;
  roleFilter?: string[];
  minScore: number;
  userEmail?: string;
  userIp: string;
  userAgent?: string;
}

/**
 * Send Discord notification for prompt usage
 */
export async function sendPromptUsageNotification(
  data: PromptUsageNotificationData
): Promise<void> {
  try {
    await DISCORD_WEBHOOK.send({
      embeds: [
        {
          title: "🚀 Prompt Used",
          description: `A user just used a prompt solution!`,
          color: 0x00ff00, // Green color
          fields: [
            {
              name: "Solution Name",
              value: data.solutionName || "Unknown",
              inline: true,
            },
            {
              name: "Solution Link",
              value: `[View Solution](https://dothistask.ai/solution/${data.solutionId})`,
              inline: true,
            },
            {
              name: "Total Usage Count",
              value: String(data.usageCount),
              inline: true,
            },
            {
              name: data.userEmail ? "User" : "User IP",
              value: data.userEmail ? `||${data.userEmail}||` : data.userIp,
              inline: true,
            },
            {
              name: "User Agent",
              value:
                (data.userAgent || "Unknown").slice(0, 100) +
                (data.userAgent && data.userAgent.length > 100 ? "..." : ""),
              inline: false,
            },
          ],
          timestamp: new Date().toISOString(),
        },
      ],
    });
  } catch (error) {
    console.error(
      "Failed to send Discord notification for prompt usage:",
      error
    );
  }
}

/**
 * Send Discord notification for search events
 */
export async function sendSearchNotification(
  data: SearchNotificationData
): Promise<void> {
  try {
    await DISCORD_WEBHOOK.send({
      embeds: [
        {
          title: "🔍 Search Performed",
          description: `A user performed a search query!`,
          color: 0x0099ff, // Blue color
          fields: [
            {
              name: "Search Query",
              value: `\`${data.query.slice(0, 200)}${data.query.length > 200 ? "..." : ""}\``,
              inline: false,
            },
            {
              name: "Results Found",
              value: String(data.resultsFound),
              inline: true,
            },
            {
              name: "Total Count",
              value: String(data.totalCount),
              inline: true,
            },
            {
              name: "Has Next Page",
              value: data.hasNextPage ? "Yes" : "No",
              inline: true,
            },
            {
              name: "Role Filter",
              value:
                data.roleFilter && data.roleFilter.length > 0
                  ? data.roleFilter.join(", ")
                  : "None",
              inline: true,
            },
            {
              name: "Min Score",
              value: String(data.minScore),
              inline: true,
            },
            {
              name: data.userEmail ? "User" : "User IP",
              value: data.userEmail ? `||${data.userEmail}||` : data.userIp,
              inline: true,
            },
            {
              name: "User Agent",
              value:
                (data.userAgent || "Unknown").slice(0, 100) +
                (data.userAgent && data.userAgent.length > 100 ? "..." : ""),
              inline: false,
            },
          ],
          timestamp: new Date().toISOString(),
        },
      ],
    });
  } catch (error) {
    console.error("Failed to send Discord notification for search:", error);
  }
}
