import { DISCORD_WEBHOOK } from "@/libs/discord";
import { TRPCError } from "@trpc/server";
import { randomUUID } from "crypto";

// Define the interface for the error reporting parameters
export interface ErrorReportParams {
  path: string;
  type: string;
  rawInput: unknown;
  error: unknown;
  ip: string;
  userId: string;
  email: string;
}

// Helper function to report exceptions
export async function reportException({
  path,
  type,
  rawInput,
  error,
  ip,
  userId,
  email,
}: ErrorReportParams) {
  const requestId = randomUUID();
  const errorMessage = error instanceof Error ? error.message : String(error);
  const stack =
    errorMessage +
    ": " +
    (error instanceof Error
      ? error.stack || JSON.stringify(error)
      : JSON.stringify(error));

  try {
    // First message: Embed with metadata
    await DISCORD_WEBHOOK.send({
      embeds: [
        {
          title: "🚨 tRPC Error",
          fields: [
            {
              name: "Request ID",
              value: requestId,
              inline: true,
            },
            {
              name: "Path",
              value: path || "Unknown",
              inline: true,
            },
            {
              name: "Type",
              value: type || "Unknown",
              inline: true,
            },
            {
              name: "IP",
              value: ip || "Unknown",
              inline: true,
            },
            {
              name: "Error Code",
              value:
                error instanceof TRPCError
                  ? error.code
                  : "INTERNAL_SERVER_ERROR",
              inline: true,
            },
            {
              name: "User ID",
              value: userId || "Unknown",
              inline: true,
            },
            {
              name: "Email",
              value: email || "Unknown",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
          color: 0xff0000, // Red color
        },
      ],
    });

    // Second message: Input and error details
    const truncatedError =
      stack.length > 4000 ? stack.slice(0, 4000) + "..." : stack;
    const inputString = JSON.stringify(rawInput);

    await DISCORD_WEBHOOK.send(
      `**Request ID: ${requestId}**\nInput:\n\`\`\`\n${inputString}\`\`\`\nError:\n\`\`\`\n${truncatedError}\`\`\``
    );
  } catch (webhookError) {
    console.error("Failed to send error to Discord:", webhookError);
  }
}
