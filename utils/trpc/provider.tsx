"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { httpBatchLink } from "@trpc/client";
import { useState } from "react";
import superjson from "superjson";
import { trpc } from "./client";

export function TRPCProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Cache data for 5 minutes before considering it stale
            staleTime: 1000 * 60 * 5, // 5 minutes
            // Keep data in cache for 10 minutes
            cacheTime: 1000 * 60 * 10, // 10 minutes
            // Don't refetch on window focus to prevent spam
            refetchOnWindowFocus: false,
            // Allow refetch on mount for fresh data, but only if stale
            refetchOnMount: true,
            // Don't refetch on reconnect to prevent spam
            refetchOnReconnect: false,
            // Retry failed requests 2 times with exponential backoff
            retry: 2,
            // Prevent background refetches
            refetchInterval: false,
            // Don't refetch when component remounts if data is still fresh
            refetchIntervalInBackground: false,
          },
        },
      })
  );

  const [trpcClient] = useState(() =>
    trpc.createClient({
      transformer: superjson,
      links: [
        httpBatchLink({
          url: "/api/trpc",
        }),
      ],
    })
  );

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  );
}
