import { cache } from "./cache";

/**
 * Cache invalidation utilities to maintain data consistency
 */
export class CacheInvalidator {
  /**
   * Clear all role-related caches
   */
  async invalidateRoles(): Promise<void> {
    await Promise.all([
      cache.del("all-roles", { prefix: "roles" }),
      // Add pattern-based deletion for role-specific caches if needed
    ]);
  }

  /**
   * Clear search-related caches
   * @param options - Optional filters to invalidate specific search caches
   */
  async invalidateSearch(options?: {
    roleFilter?: string[];
    query?: string;
  }): Promise<void> {
    // For now, we'll clear all search caches
    // In the future, could implement pattern-based deletion
    console.log("Search cache invalidation triggered", options);
    // Note: Redis doesn't have built-in pattern deletion with Upstash
    // For now, we rely on TTL expiration
  }

  /**
   * Clear task-related caches
   */
  async invalidateTasks(): Promise<void> {
    // Clear browse caches (first page results)
    await Promise.all([
      cache.del("l:20|c:none|rf:none", { prefix: "tasks" }),
      // Could add more specific cache keys here
    ]);
  }

  /**
   * Clear all caches (nuclear option)
   */
  async invalidateAll(): Promise<void> {
    await Promise.all([
      this.invalidateRoles(),
      this.invalidateSearch(),
      this.invalidateTasks(),
    ]);
  }
}

export const cacheInvalidator = new CacheInvalidator();
