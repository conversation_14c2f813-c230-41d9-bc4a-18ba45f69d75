import { redis } from "./client";

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

export class RedisCache {
  private defaultTTL = 300; // 5 minutes
  private defaultPrefix = "cache";

  private isBuildTime(): boolean {
    return (
      process.env.NODE_ENV === "production" &&
      process.env.NEXT_PHASE === "phase-production-build"
    );
  }

  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    // Skip Redis during build time
    if (this.isBuildTime()) {
      return null;
    }

    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const data = await redis.get(fullKey);
      if (data === null) return null;

      // Transform the data to handle Date strings
      return this.transformDatesFromCache(data) as T;
    } catch (error) {
      console.error("Redis cache get error:", error);
      return null;
    }
  }

  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    // Skip Redis during build time
    if (this.isBuildTime()) {
      return;
    }

    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const ttl = options?.ttl ?? this.defaultTTL;
      await redis.setex(fullKey, ttl, JSON.stringify(value));
    } catch (error) {
      console.error("Redis cache set error:", error);
    }
  }

  async del(key: string, options?: CacheOptions): Promise<void> {
    // Skip Redis during build time
    if (this.isBuildTime()) {
      return;
    }

    try {
      const fullKey = this.buildKey(key, options?.prefix);
      await redis.del(fullKey);
    } catch (error) {
      console.error("Redis cache del error:", error);
    }
  }

  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    // During build time, just call the fetcher directly without Redis
    if (this.isBuildTime()) {
      return await fetcher();
    }

    try {
      // Try to get from cache first
      const cached = await this.get<T>(key, options);
      if (cached !== null) {
        console.log(`Cache hit for key: ${key}`);
        return cached;
      }

      console.log(`Cache miss for key: ${key}, fetching data...`);
      // Not in cache, fetch the data
      const data = await fetcher();

      // Set in cache for next time
      await this.set(key, data, options);

      return data;
    } catch (error) {
      console.error(`Cache getOrSet error for key ${key}:`, error);
      // If caching fails, fall back to fetcher
      return await fetcher();
    }
  }

  private transformDatesFromCache(obj: any): any {
    if (obj === null || obj === undefined) return obj;

    if (typeof obj === "string" && this.isISODateString(obj)) {
      return new Date(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.transformDatesFromCache(item));
    }

    if (typeof obj === "object") {
      const transformed: any = {};
      for (const [key, value] of Object.entries(obj)) {
        transformed[key] = this.transformDatesFromCache(value);
      }
      return transformed;
    }

    return obj;
  }

  private isISODateString(str: string): boolean {
    if (typeof str !== "string") return false;

    // Check if it's a valid ISO date string format
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
    if (!isoDateRegex.test(str)) return false;

    // Check if it's a valid date
    const date = new Date(str);
    return !isNaN(date.getTime()) && date.toISOString() === str;
  }

  private buildKey(key: string, prefix?: string): string {
    const actualPrefix = prefix ?? this.defaultPrefix;
    return `dtta:${actualPrefix}:${key}`;
  }
}

export const cache = new RedisCache();
