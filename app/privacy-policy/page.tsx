import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import config from "@/config";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// CHATGPT PROMPT TO GENERATE YOUR PRIVACY POLICY — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.
// Write a **clear, legally sound** Privacy Policy for my website.

// Here is the required context:
// - **Website**: https://dothistask.ai
// - **Name**: DoThisTaskAI
// - **Description**: A process identification and solution generation platform meant to connect knowledge workers with AI solutions that help them automate time consuming and repetitive processes at their jobs.
// - **Data Collection**:
//   - **Personal Data**: Name, email, role at company, job process data, task definitions, AI solution usage data.
//   - **Non-Personal Data**: Web cookies, usage analytics, search queries.
// - **Purpose**: Platform functionality, user authentication, solution recommendations, process automation, analytics for platform improvement.
// - **Data Sharing**:
//   - **No third-party data sharing** except as required for platform functionality (authentication, payment processing).
//   - **Premium and enterprise users** may mark their AI solutions as "private" to prevent them from being publicly shared with other users.
//   - **Enterprise customers** may access their organization's usage data and generated solutions.
//   - **Aggregated, anonymized** usage data may be used for platform improvement.
// - **Data Retention**: We retain user data indefinitely unless a user requests deletion. Users may contact <NAME_EMAIL> to request data removal, which will be processed within 30 days
// - **User Rights**: Users can request data deletion or modification.
// - **Children's Privacy**: We do not collect data from children.
// - **Updates**: Users will be notified by email.
// - **Contact**: <EMAIL>
// - **Current Date**: 07/06/2025

// Ensure the policy is **concise, clear, and legally enforceable**. **Do not explain or add extra details.**

// Turn this document into code similar to the following component:

const PRIVACY_POLICY_CONTENT = `# Privacy Policy for DoThisTaskAI

**Last Updated: July 6, 2025**

Thank you for visiting DoThisTaskAI ("we," "us," or "our"). This Privacy Policy outlines how we collect, use, and protect your personal and non-personal information when you use our website located at https://dothistask.ai (the "Website").

By accessing or using the Website, you agree to the terms of this Privacy Policy. If you do not agree with the practices described in this policy, please do not use the Website.

## 1. Information We Collect

### 1.1 Personal Data

We collect the following personal information from you:

- **Name**: We collect your name to personalize your experience and communicate with you effectively.
- **Email**: We collect your email address for user authentication, platform communication, and important updates.
- **Role at Company**: We collect your professional role information to provide tailored solution recommendations.
- **Job Process Data**: We collect information about your work processes to generate relevant AI solutions and automation recommendations.
- **Task Definitions**: We collect task-related data that you input to provide customized solutions and improve our platform.
- **AI Solution Usage Data**: We track how you interact with AI solutions to improve platform functionality and user experience.

### 1.2 Non-Personal Data

We may use web cookies and similar technologies to collect non-personal information such as:

- Web cookies for session management and user preferences
- Usage analytics to understand platform performance and user behavior
- Search queries to improve our search functionality and solution recommendations
- IP address, browser type, device information, and browsing patterns
- Website interaction data including page views, clicks, form submissions, and navigation patterns
- Session data and user journey tracking to understand how you use our platform
- Feature usage analytics to measure engagement with specific platform features
- Referral sources and marketing attribution data
- A/B testing and feature experimentation data
- Performance metrics and error tracking data

This information helps us enhance your experience, analyze trends, improve our services, and optimize platform performance.

## 2. Purpose of Data Collection

We collect and use your personal data for the following purposes:

- **Platform Functionality**: To provide core features and services of our process identification and solution generation platform
- **User Authentication**: To securely identify and authenticate users
- **Solution Recommendations**: To provide personalized AI solutions that match your specific work processes and needs
- **Process Automation**: To help automate time-consuming and repetitive processes at your job
- **Analytics for Platform Improvement**: To analyze usage patterns, user behavior, and feature engagement to improve our platform features and performance
- **Service Optimization**: To conduct A/B testing, feature experimentation, and performance optimization
- **User Experience Enhancement**: To understand user journeys and optimize the platform interface and functionality

## 3. Data Sharing

We are committed to protecting your privacy and do not share your personal data with third parties except in the following limited circumstances:

- **Platform Functionality**: We may share data with trusted third-party services as required for essential platform functionality, including authentication and payment processing
- **Private Solutions**: Premium and enterprise users may mark their AI solutions as "private" to prevent them from being publicly shared with other users
- **Enterprise Access**: Enterprise customers may access their organization's usage data and generated solutions within their account
- **Aggregated Data**: We may use aggregated, anonymized usage data for platform improvement and analytics

We do not sell, trade, or rent your personal information to others.

## 4. Data Retention

We retain user data indefinitely unless a user requests deletion. Users may contact <NAME_EMAIL> to request data removal, which will be processed within 30 days of receiving the request.

## 5. User Rights

You have the following rights regarding your personal data:

- **Access**: You can request access to the personal data we hold about you
- **Modification**: You can request that we correct or update your personal information
- **Deletion**: You can request that we delete your personal data
- **Portability**: You can request a copy of your data in a structured, machine-readable format

To exercise any of these rights, please contact <NAME_EMAIL>.

## 6. Children's Privacy

DoThisTaskAI is not intended for children under the age of 13. We do not knowingly collect personal information from children. If you are a parent or guardian and believe that your child has provided us with personal information, please contact us at the email address provided below.

## 7. Updates to the Privacy Policy

We may update this Privacy Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. Any updates will be posted on this page, and we will notify you via email about significant changes.

## 8. Contact Information

If you have any questions, concerns, or requests related to this Privacy Policy, you can contact us at:

**Email**: <EMAIL>

For all other inquiries, please visit our Contact Us page on the Website.

By using DoThisTaskAI, you consent to the terms of this Privacy Policy.`;

export const metadata = getSEOTags({
  title: `Privacy Policy | ${config.metadata.appName}`,
  canonicalUrlRelative: "/privacy-policy",
});

const PrivacyPolicy = () => {
  return (
    <main className="max-w-xl mx-auto bg-background text-foreground">
      <div className="p-5">
        <Link
          href="/"
          className="inline-flex items-center gap-2 px-3 py-2 rounded-md text-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
              clipRule="evenodd"
            />
          </svg>{" "}
          Back
        </Link>
        <h1 className="text-3xl font-extrabold pb-6 text-foreground">
          Privacy Policy for {config.metadata.appName}
        </h1>

        <div className="prose prose-sm max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              h1: ({ children }) => (
                <h1 className="text-2xl font-bold mb-4 mt-6 text-foreground">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-xl font-semibold mb-3 mt-6 text-foreground">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-lg font-medium mb-2 mt-4 text-foreground">
                  {children}
                </h3>
              ),
              p: ({ children }) => (
                <p className="mb-3 leading-relaxed text-foreground">
                  {children}
                </p>
              ),
              ul: ({ children }) => (
                <ul className="list-disc ml-6 mb-3 space-y-1 text-foreground">
                  {children}
                </ul>
              ),
              li: ({ children }) => (
                <li className="leading-relaxed text-foreground">{children}</li>
              ),
              strong: ({ children }) => (
                <strong className="font-semibold text-foreground">
                  {children}
                </strong>
              ),
              a: ({ children, href, ...props }) => (
                <a
                  href={href}
                  className="text-primary hover:text-primary/80 underline transition-colors"
                  {...props}
                >
                  {children}
                </a>
              ),
            }}
          >
            {PRIVACY_POLICY_CONTENT}
          </ReactMarkdown>
        </div>
      </div>
    </main>
  );
};

export default PrivacyPolicy;
