import { getRoleBySlug, getSolutionById } from "@/lib/database";
import { notFound } from "next/navigation";
import { SolutionItem } from "@/types/models/solution";
import { RoleItem } from "@/types/models/role";
import { SolutionPage } from "@/components/pages/solution-page";
import {
  getRoleSolutionSEOTags,
  getRoleSolutionStructuredData,
} from "@/libs/seo";
import Script from "next/script";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";
import { Breadcrumbs } from "@/components/breadcrumbs";

export const revalidate = 300;

interface SolutionPageProps {
  params: Promise<{ roleSlug: string; solutionId: string }>;
}

export async function generateMetadata({ params }: SolutionPageProps) {
  const { roleSlug, solutionId } = await params;

  const [role, solution] = await Promise.all([
    getRoleBySlug({ slug: roleSlug }),
    getSolutionById({ id: solutionId }),
  ]);

  if (!role || !solution) {
    return {
      title: "Page Not Found",
      description: "The requested role or solution could not be found.",
    };
  }

  const plainRole: RoleItem = JSON.parse(JSON.stringify(role));
  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));

  return getRoleSolutionSEOTags({
    role: plainRole,
    solution: plainSolution,
    roleSlug,
  });
}

export default async function RoleSolutionPage({ params }: SolutionPageProps) {
  const { roleSlug, solutionId } = await params;

  // Get role and solution for breadcrumbs and component
  const [role, solution] = await Promise.all([
    getRoleBySlug({ slug: roleSlug }),
    getSolutionById({ id: solutionId }),
  ]);

  if (!role || !solution) {
    notFound();
  }

  // Get collections for header
  const collections = await getUserCollectionsServer();

  // Convert for breadcrumbs and component
  const plainRole: RoleItem = JSON.parse(JSON.stringify(role));
  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));

  const breadcrumbItems = [
    { label: "Roles", href: "/roles" },
    { label: plainRole.roleName, href: `/roles/${roleSlug}` },
    { label: plainSolution.name },
  ];

  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        type="application/ld+json"
        id={`json-ld-role-solution-${roleSlug}-${solutionId}`}
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            getRoleSolutionStructuredData({
              role: plainRole,
              solution: plainSolution,
              roleSlug,
            })
          ),
        }}
      />

      <AppHeader collections={collections} />

      <div className="container mx-auto p-6">
        <Breadcrumbs items={breadcrumbItems} />
        <SolutionPage solution={plainSolution} />
      </div>
    </>
  );
}
