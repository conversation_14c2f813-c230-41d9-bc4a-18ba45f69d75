import { getRoleBySlug } from "@/lib/database";
import { notFound } from "next/navigation";
import { RoleItem } from "@/types/models/role";
import { getRoleSEOTags, getRoleStructuredData } from "@/libs/seo";
import Script from "next/script";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";
import { RolePage } from "@/components/pages/role-page";
import { Breadcrumbs } from "@/components/breadcrumbs";

// Revalidate this page every 30 minutes (1800 seconds)
export const revalidate = 300;

interface RolePageProps {
  params: Promise<{ roleSlug: string }>;
}

export async function generateMetadata({ params }: RolePageProps) {
  const { roleSlug } = await params;

  const role = await getRoleBySlug({ slug: roleSlug });

  if (!role) {
    return {
      title: "Role Not Found",
      description: "The requested role could not be found.",
    };
  }

  const plainRole: RoleItem = JSON.parse(JSON.stringify(role));
  return getRoleSEOTags(plainRole, roleSlug);
}

export default async function RolePageRoute({ params }: RolePageProps) {
  const { roleSlug } = await params;

  // Only breadcrumb-specific logic: get role name for breadcrumbs
  const role = await getRoleBySlug({ slug: roleSlug });

  if (!role) {
    notFound();
  }

  // Convert for breadcrumbs
  const plainRole: RoleItem = JSON.parse(JSON.stringify(role));

  // Get collections for header
  const collections = await getUserCollectionsServer();

  const breadcrumbItems = [
    { label: "Roles", href: "/roles" },
    { label: plainRole.roleName },
  ];

  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        type="application/ld+json"
        id={`json-ld-role-${roleSlug}`}
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getRoleStructuredData(plainRole, roleSlug)),
        }}
      />

      <AppHeader collections={collections} />

      <Breadcrumbs items={breadcrumbItems} />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <RolePage
          roleSlug={roleSlug}
          solutionLinkPattern={`/roles/${roleSlug}/solutions/{solutionId}`}
        />
      </div>
    </>
  );
}
