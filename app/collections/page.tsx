import { CollectionsPage } from "@/components/pages/collections-page";
import { Breadcrumbs } from "@/components/breadcrumbs";

export const dynamic = "force-dynamic";

export default async function CollectionsPageRoute() {
  const breadcrumbItems: Array<{ label: string; href?: string }> = []; // Collections page has no breadcrumbs

  return (
    <>
      <Breadcrumbs items={breadcrumbItems} />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <CollectionsPage />
      </div>
    </>
  );
}
