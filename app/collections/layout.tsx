import { type ReactNode } from "react";
import { InsightoFeedbackButton } from "@/components/InsightoFeedbackButton";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";

export default async function CollectionsLayout({
  children,
}: {
  children: ReactNode;
}) {
  // Fetch collections on the server side (returns empty array if not authenticated)
  const collections = await getUserCollectionsServer();

  return (
    <div className="min-h-screen bg-background">
      <AppHeader collections={collections} />
      <main className="w-full">{children}</main>
      <InsightoFeedbackButton />
    </div>
  );
}
