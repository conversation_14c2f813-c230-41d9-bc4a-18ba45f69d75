import { CollectionDetailPage } from "@/components/pages/collection-detail-page";
import { Breadcrumbs } from "@/components/breadcrumbs";
import Collection from "@/models/Collection";
import connectMongo from "@/libs/mongoose";
import { notFound } from "next/navigation";
import { collectionDetailMongooseParser } from "@/types/models/collection";

export const dynamic = "force-dynamic";

interface CollectionPageProps {
  params: {
    collectionId: string;
  };
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  await connectMongo();

  // Fetch the collection with populated tasks for the component
  const collection = await Collection.findOne({
    _id: params.collectionId,
  })
    .populate({
      path: "tasks",
      populate: {
        path: "roles",
        model: "Role",
      },
    })
    .lean();

  if (!collection) {
    notFound();
  }

  // Transform data for client using Zod parser
  const collectionData = collectionDetailMongooseParser.parse(collection);

  const breadcrumbItems = [
    { label: "Collections", href: "/collections" },
    { label: collection.name },
  ];

  return (
    <>
      <Breadcrumbs items={breadcrumbItems} />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <CollectionDetailPage
          collection={collectionData}
          solutionLinkPattern={`/collections/${params.collectionId}/solutions/{solutionId}`}
        />
      </div>
    </>
  );
}
