import { getSolutionById } from "@/lib/database";
import { notFound, redirect } from "next/navigation";
import { SolutionItem } from "@/types/models/solution";
import { SolutionPage } from "@/components/pages/solution-page";
import { getSolutionSEOTags, getSolutionStructuredData } from "@/libs/seo";
import Script from "next/script";
import Collection from "@/models/Collection";
import connectMongo from "@/libs/mongoose";
import { Breadcrumbs } from "@/components/breadcrumbs";
import { authOptions } from "@/libs/next-auth";
import { getServerSession } from "next-auth";

export const revalidate = 300;

interface SolutionPageProps {
  params: Promise<{ collectionId: string; solutionId: string }>;
}

export async function generateMetadata({ params }: SolutionPageProps) {
  const { solutionId } = await params;

  const solution = await getSolutionById({ id: solutionId });

  if (!solution) {
    return {
      title: "Solution Not Found",
      description: "The requested solution could not be found.",
    };
  }

  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));
  return getSolutionSEOTags(plainSolution);
}

export default async function CollectionSolutionPage({
  params,
}: SolutionPageProps) {
  const session = await getServerSession(authOptions);
  const { collectionId, solutionId } = await params;

  // Get collection and solution for breadcrumbs and component
  const solution = await getSolutionById({ id: solutionId });

  if (!solution) {
    notFound();
  }

  await connectMongo();
  const collection = await Collection.findById(collectionId).lean();

  if (!collection) {
    redirect(`/solutions/${solutionId}`);
  }

  const hasAccessToCollection =
    collection.isPublic || collection.userId.toString() === session?.user?.id;

  if (!hasAccessToCollection) {
    redirect(`/solutions/${solutionId}`);
  }

  // Convert for breadcrumbs and component
  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));

  const breadcrumbItems = [
    { label: "Collections", href: "/collections" },
    { label: collection.name, href: `/collections/${collectionId}` },
    { label: plainSolution.name },
  ];

  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        type="application/ld+json"
        id={`json-ld-collection-solution-${collectionId}-${solutionId}`}
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getSolutionStructuredData(plainSolution)),
        }}
      />

      <Breadcrumbs items={breadcrumbItems} />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <SolutionPage solution={plainSolution} />
      </div>
    </>
  );
}
