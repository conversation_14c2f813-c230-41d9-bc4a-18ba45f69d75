import { Suspense } from "react";
import HeaderBlog from "./_assets/components/HeaderBlog";
import Footer from "@/components/Footer";
import env from "@/libs/env";
import Header from "@/components/Header";

export default async function LayoutBlog({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <Suspense>{env.BLOG_ENABLED_FLAG ? <HeaderBlog /> : <Header />}</Suspense>

      {env.BLOG_ENABLED_FLAG ? (
        <main className="min-h-screen max-w-6xl mx-auto p-8">{children}</main>
      ) : (
        <main className="w-full flex items-center justify-center p-8">
          <div> Blog is disabled</div>
        </main>
      )}
      <div className="h-24" />

      <Footer />
    </div>
  );
}
