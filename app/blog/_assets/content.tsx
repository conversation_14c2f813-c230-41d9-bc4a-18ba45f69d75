import { type articleType } from "./articles/types";
import introducingSupabaseArticle from "./articles/introducing-supabase";

// Re-export types and constants from articles/types
export { categories, authors } from "./articles/types";
export type { articleType } from "./articles/types";

/**
 * This file is an index file for the blog articles.
 */

// All the blog articles data display in the /blog/[articleId].js pages.
export const articles: articleType[] = [introducingSupabaseArticle];
