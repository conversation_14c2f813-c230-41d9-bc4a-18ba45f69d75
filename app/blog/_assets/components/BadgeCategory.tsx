import Link from "next/link";
import type { categoryType } from "../articles/types";
import { Badge } from "@/components/ui/badge";

// This is the category badge that appears in the article page and in <CardArticle /> component
const Category = ({
  category,
  extraStyle,
}: {
  category: categoryType;
  extraStyle?: string;
}) => {
  return (
    <Link
      href={`/blog/category/${category.slug}`}
      title={`Posts in ${category.title}`}
      rel="tag"
      className="hover:no-underline"
    >
      <Badge variant="secondary" className={extraStyle}>
        {category.titleShort}
      </Badge>
    </Link>
  );
};

export default Category;
