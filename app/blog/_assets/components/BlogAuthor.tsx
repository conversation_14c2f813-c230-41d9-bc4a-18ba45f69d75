import Link from "next/link";
import type { articleType } from "../content";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// This is the author avatar that appears in the article page and in <CardArticle /> component
const BlogAuthor = ({ article }: { article: articleType }) => {
  return (
    <Link
      href={`/blog/author/${article.author.slug}`}
      title={`Posts by ${article.author.name}`}
      className="inline-flex items-center gap-2 group"
      rel="author"
    >
      <span itemProp="author">
        <Avatar>
          <AvatarImage src={article.author.avatar} />
          <AvatarFallback>{article.author.name.charAt(0)}</AvatarFallback>
        </Avatar>
      </span>
      <span className="group-hover:underline">{article.author.name}</span>
    </Link>
  );
};

export default BlogAuthor;
