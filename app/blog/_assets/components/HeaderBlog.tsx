"use client";

import Link from "next/link";
import Image from "next/image";
import ButtonSignin from "@/components/ButtonSignin";
import logo from "@/app/icon.png";
import config from "@/config";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import { Menu, ChevronDown } from "lucide-react";
import { ThemePicker } from "@/components/ThemePicker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { categories } from "../content";

const links: {
  href: string;
  label: string;
}[] = [
  {
    href: "/blog/",
    label: "All Posts",
  },
];

const cta: JSX.Element = <ButtonSignin />;

const CategoryPopover = () => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="flex items-center gap-1">
          Categories
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-2">
        <div className="grid gap-2">
          {categories.map((category) => (
            <Link
              key={category.slug}
              className="block p-3 hover:bg-muted rounded-md transition-colors"
              href={`/blog/category/${category.slug}`}
            >
              <p className="font-medium mb-0.5">
                {category?.titleShort || category.title}
              </p>
              <p className="text-sm text-muted-foreground">
                {category?.descriptionShort || category.description}
              </p>
            </Link>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

const HeaderBlog = () => {
  return (
    <header className="backdrop-blur-sm bg-background/80 sticky top-0 z-40 border-b w-full">
      <div className="max-w-5xl mx-auto flex items-center justify-between px-4 sm:px-6 py-3">
        {/* Logo */}
        <div className="flex-shrink-0">
          <Link
            className="flex items-center gap-2"
            href="/"
            title={`${config.metadata.appName} homepage`}
          >
            <Image
              src={logo}
              alt={`${config.metadata.appName} logo`}
              className="w-7 h-7 sm:w-8 sm:h-8"
              priority={true}
              width={32}
              height={32}
            />
            <span className="font-bold text-base sm:text-lg">
              {config.metadata.appName}
            </span>
          </Link>
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:flex items-center gap-8">
          <nav className="flex items-center gap-6">
            {links.map((link) => (
              <Link
                href={link.href}
                key={link.href}
                className="text-muted-foreground hover:text-foreground transition font-medium"
              >
                {link.label}
              </Link>
            ))}
            <CategoryPopover />
          </nav>
          <div className="flex items-center gap-2">
            <ThemePicker variant="lg" />
            {cta}
          </div>
        </div>

        {/* Mobile menu */}
        <Sheet>
          <div className="flex items-center gap-2 md:hidden">
            <ThemePicker variant="lg" />
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Open menu</span>
              </Button>
            </SheetTrigger>
          </div>
          <SheetContent side="right" className="w-64 sm:max-w-sm">
            <div className="flex flex-col gap-4 mt-8">
              {links.map((link) => (
                <SheetClose asChild key={link.href}>
                  <Link
                    href={link.href}
                    className="text-foreground hover:text-primary py-2 font-medium text-lg"
                  >
                    {link.label}
                  </Link>
                </SheetClose>
              ))}
              <div className="flex flex-col gap-2">
                <p className="font-medium text-lg">Categories</p>
                {categories.map((category) => (
                  <SheetClose asChild key={category.slug}>
                    <Link
                      href={`/blog/category/${category.slug}`}
                      className="text-muted-foreground hover:text-primary py-1 pl-4"
                    >
                      {category?.titleShort || category.title}
                    </Link>
                  </SheetClose>
                ))}
              </div>
            </div>

            <div className="mt-4">
              <SheetClose asChild>
                <div>{cta}</div>
              </SheetClose>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
};

export default HeaderBlog;
