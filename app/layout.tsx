import ClientLayout from "@/components/LayoutClient";
import { PostHogProvider } from "@/components/PostHogProvider";
import config from "@/config";
import { getSEOTags } from "@/libs/seo";
import { type Viewport } from "next";
import { Inter } from "next/font/google";
import { type ReactNode } from "react";
import "./globals.css";
import { register } from "@/instrumentation";

const font = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  // Will use the primary color of your theme to show a nice theme color in the URL bar of supported browsers
  themeColor: config.colors.main,
  width: "device-width",
  initialScale: 1,
};

register();

// This adds default SEO tags to all pages in our app.
// You can override them in each page passing params to getSOTags() function.
export const metadata = getSEOTags();

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" data-theme={config.colors.theme} className={font.className}>
      <body className="min-h-screen bg-background text-foreground">
        <PostHogProvider>
          {/* ClientLayout contains all the client wrappers (Crisp chat support, toast messages, tooltips, etc.) */}
          <ClientLayout>{children}</ClientLayout>
        </PostHogProvider>
      </body>
    </html>
  );
}
