import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { getPricingData } from "@/libs/stripe";
import { PLANS } from "@/libs/pricing";
import connectMongo from "@/libs/mongoose";
import User from "@/models/User";
import { redirect } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckIcon } from "lucide-react";
import ButtonCheckout from "@/components/ButtonCheckout";
import type Stripe from "stripe";

// Helper function to find plan by Stripe price ID
const findPlanByPriceId = (priceId: string) => {
  return Object.entries(PLANS).find(
    ([_, plan]) => "stripePriceId" in plan && plan.stripePriceId === priceId
  );
};

// Helper function to format price
const formatPrice = (price: Stripe.Price) => {
  if (!price.unit_amount) return "Contact for pricing";

  const amount = price.unit_amount / 100; // Convert from cents
  const currency = price.currency.toUpperCase();

  return `${currency === "USD" ? "$" : currency}${amount}`;
};

export default async function UserDashboard() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    redirect("/auth/signin");
  }

  await connectMongo();

  // Get user data
  const user = await User.findOne({ email: session.user.email });

  if (!user) {
    redirect("/auth/signin");
  }

  // Fetch Stripe pricing data
  const stripePrices = await getPricingData();

  // Find current user's plan
  const currentPlanEntry = user.priceId
    ? findPlanByPriceId(user.priceId)
    : (["free", PLANS.free] as const);
  const currentPlanId = currentPlanEntry?.[0];

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">User Dashboard</h1>

        {/* User Info Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>
              Your account details and subscription status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Email
                </p>
                <p className="text-lg">{user.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Name
                </p>
                <p className="text-lg">{user.name || "Not provided"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Account Status
                </p>
                <div className="flex pt-1 items-center gap-2">
                  <Badge variant={user.hasAccess ? "default" : "secondary"}>
                    {user.hasAccess ? "Active" : "No plan"}
                  </Badge>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Member Since
                </p>
                <p className="text-lg">
                  {user.createdAt
                    ? new Date(user.createdAt).toLocaleDateString()
                    : "Unknown"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Available Plans Section */}
        <Card>
          <CardHeader>
            <CardTitle>Available Plans</CardTitle>
            <CardDescription>
              Upgrade or change your subscription plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(PLANS).map(([planId, plan]) => {
                const stripePrice =
                  "stripePriceId" in plan && plan.stripePriceId
                    ? stripePrices.find(
                        (price) => price.id === plan.stripePriceId
                      )
                    : null;
                const isCurrentPlan = currentPlanId === planId;

                return (
                  <div
                    key={planId}
                    className={`p-6 border rounded-lg flex flex-col ${
                      isCurrentPlan
                        ? "border-primary bg-primary/5"
                        : "border-border"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold">{plan.name}</h3>
                      {isCurrentPlan && (
                        <Badge variant="default">Current</Badge>
                      )}
                    </div>
                    <p className="text-2xl font-bold mb-4">
                      {stripePrice ? formatPrice(stripePrice) : "$0"}
                      {stripePrice?.recurring && (
                        <span className="text-sm text-muted-foreground ml-1">
                          / {stripePrice.recurring.interval}
                        </span>
                      )}
                    </p>
                    <div className="space-y-3 mb-6 flex-1">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckIcon className="h-4 w-4 text-primary flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className="mt-auto">
                      {isCurrentPlan ? (
                        <Button disabled className="w-full">
                          Current Plan
                        </Button>
                      ) : (
                        "stripePriceId" in plan && (
                          <ButtonCheckout
                            priceId={plan.stripePriceId}
                            mode="subscription"
                            className="w-full"
                            planName={plan.name}
                            isChange={true}
                          />
                        )
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
