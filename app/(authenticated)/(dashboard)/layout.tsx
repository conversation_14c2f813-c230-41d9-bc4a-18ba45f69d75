import { type ReactNode } from "react";
import { InsightoFeedbackButton } from "@/components/InsightoFeedbackButton";
import { authorizationForServerComponent } from "@/lib/component-permissions-guard";
import OnboardingWizard from "@/components/onboarding/OnboardingWizard";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { user } = await authorizationForServerComponent({
    hasAccessRequired: false,
  });

  if (user.onboarding.completed === false) {
    return <OnboardingWizard user={user} />;
  }

  // Fetch collections on the server side
  const collections = await getUserCollectionsServer();

  return (
    <div className="min-h-screen bg-background">
      <AppHeader collections={collections} />
      <main className="w-full">{children}</main>
      <InsightoFeedbackButton />
    </div>
  );
}
