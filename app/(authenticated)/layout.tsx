import config from "@/config";
import { authOptions } from "@/libs/next-auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { type ReactNode } from "react";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";

// This is a server-side component to ensure the user is logged in.
// If not, it will redirect to the login page.
// It's applied to all pages in (authenticated)/
// See https://shipfa.st/docs/tutorials/private-page
export default async function LayoutAuthenticated({
  children,
}: {
  children: ReactNode;
}) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect(config.auth.loginUrl);
  }

  // Fetch collections for the AppHeader
  const collections = await getUserCollectionsServer();

  return (
    <div className="min-h-screen bg-background">
      <AppHeader collections={collections} />
      <main className="w-full">{children}</main>
    </div>
  );
}
