import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from "@trpc/server/adapters/fetch";
import { appRouter } from "@/server/routers/_app";
import { createContext } from "@/server/trpc";
import { headers } from "next/headers";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import User from "@/models/User";
import { reportException } from "@/utils/discord/error-reporting";

const handler = (req: Request) =>
  fetchRequestHandler({
    endpoint: "/api/trpc",
    req,
    router: appRouter,
    createContext,
    onError: async ({ error, type, path, input }) => {
      console.error(`❌ tRPC error on ${path}: ${error}`);
      let userId = null;
      let email = null;
      try {
        const session = await getServerSession(authOptions);
        userId = session?.user?.id;
        email = session?.user?.email;
        const user = await User.findOne({ _id: userId });
        if (user) {
          userId = user._id.toString();
          email = user.email;
        }
      } catch (error) {
        console.error("Error getting session", error);
      }

      await reportException({
        path: path ?? "Unknown",
        type,
        rawInput: input,
        error,
        ip: headers().get("x-forwarded-for") ?? "127.0.0.1",
        userId: userId ?? "Unknown",
        email: email ?? "Unknown",
      });
    },
  });

export { handler as GET, handler as POST };
