import env from "@/libs/env";
import connectMongo from "@/libs/mongoose";
import { findCheckoutSession } from "@/libs/stripe";
import User from "@/models/User";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import Stripe from "stripe";
import {
  ApiContext,
  ApiMethod,
  wrapApiWithManagerAndContext,
} from "@/lib/api-manager";
import { DISCORD_WEBHOOK } from "@/libs/discord";
import { retryOperation } from "@/lib/async-utils";
import { PLANS } from "@/libs/pricing";

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-08-16",
  typescript: true,
});
const webhookSecret = env.STRIPE_WEBHOOK_SECRET;

const getPlanByPriceId = (priceId: string) => {
  return Object.values(PLANS).find(
    (plan) => "stripePriceId" in plan && plan.stripePriceId === priceId
  );
};

// This is where we receive Stripe webhook events
// It used to update the user data, send emails, etc...
// By default, it'll store the user in the database
// See more: https://shipfa.st/docs/features/payments
const handlePost: ApiMethod = async (context: ApiContext) => {
  await connectMongo();

  const body = await context.getRequestText();

  const signature = headers().get("stripe-signature");
  if (!signature) {
    console.error("No signature");
    return NextResponse.json({ error: "No signature" }, { status: 400 });
  }

  let event;

  // verify Stripe event is legit
  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    if (err instanceof Error) {
      console.error(`Webhook signature verification failed. ${err.message}`);
    } else {
      console.error("Webhook signature verification failed.");
    }
    throw err;
  }

  // Handle the event
  switch (event.type) {
    case "checkout.session.completed": {
      const session = event.data.object as Stripe.Checkout.Session;

      try {
        const fullSession = await findCheckoutSession(session.id);
        const customerEmail = fullSession?.customer_details?.email;
        const customerId =
          typeof fullSession?.customer === "string"
            ? fullSession.customer
            : null;
        const price = fullSession?.line_items?.data[0]?.price;
        const priceId = price?.id;

        if (!customerEmail || !customerId || !priceId || !price) {
          console.error("Missing data in checkout session");
          await DISCORD_WEBHOOK.send({
            embeds: [
              {
                title: "❌ Stripe Webhook Error",
                description:
                  "`checkout.session.completed` event missing required data.",
                color: 0xff0000,
                fields: [
                  { name: "Session ID", value: session.id },
                  { name: "Customer Email", value: customerEmail || "NULL" },
                  { name: "Customer ID", value: customerId || "NULL" },
                  { name: "Price ID", value: priceId || "NULL" },
                ],
              },
            ],
          });
          return NextResponse.json(
            { error: `Missing data in session: ${session.id}` },
            { status: 400 }
          );
        }

        const plan = getPlanByPriceId(priceId);
        const planName = plan?.name || "Unknown Plan";
        const priceAmount = (price.unit_amount || 0) / 100;
        const priceCurrency = price.currency.toUpperCase();

        const user = await retryOperation(async () => {
          let u = await User.findOne({ email: customerEmail });
          if (!u) {
            u = await User.create({
              email: customerEmail,
              name: fullSession?.customer_details?.name,
            });
          }
          return u;
        });

        await retryOperation(() =>
          User.updateOne(
            { _id: user._id },
            {
              $set: {
                hasAccess: true,
                priceId: priceId,
                customerId: customerId,
              },
            }
          )
        );

        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "✅ Subscription Started",
              description: `User ${customerEmail} has subscribed.`,
              color: 0x00ff00,
              fields: [
                { name: "Email", value: `||${customerEmail}||`, inline: true },
                {
                  name: "Plan",
                  value: `${planName} (${priceId})`,
                  inline: true,
                },
                {
                  name: "Price",
                  value: `${priceAmount.toFixed(2)} ${priceCurrency}`,
                  inline: true,
                },
                { name: "Customer ID", value: customerId, inline: true },
              ],
              timestamp: new Date().toISOString(),
            },
          ],
        });
      } catch (error) {
        console.error("Error handling checkout.session.completed:", error);
        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "❌ Stripe Webhook Error",
              description:
                "Failed to process `checkout.session.completed` event.",
              color: 0xff0000,
              fields: [
                { name: "Session ID", value: session.id },
                { name: "Error", value: (error as Error).message },
              ],
            },
          ],
        });
        return NextResponse.json(
          { error: "Internal Server Error" },
          { status: 500 }
        );
      }
      break;
    }

    case "customer.subscription.updated": {
      const subscription = event.data.object as Stripe.Subscription;
      const customerId = subscription.customer as string;
      const price = subscription.items?.data?.[0]?.price;

      if (!price?.id) {
        const errorMessage = `customer.subscription.updated event with no price ID. Subscription ID: ${subscription.id}`;
        console.error(errorMessage);
        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "❌ Stripe Webhook Error",
              description: errorMessage,
              color: 0xff0000,
              fields: [{ name: "Subscription ID", value: subscription.id }],
            },
          ],
        });
        return NextResponse.json(
          { error: "Subscription has no price ID" },
          { status: 400 }
        );
      }

      const priceId = price.id;
      const isActive = subscription.status === "active";
      const plan = getPlanByPriceId(priceId);
      const planName = plan?.name || "Unknown Plan";
      const priceAmount = (price.unit_amount || 0) / 100;
      const priceCurrency = (price.currency || "USD").toUpperCase();

      try {
        await retryOperation(() =>
          User.updateOne(
            { customerId },
            {
              $set: {
                priceId: priceId,
                hasAccess: isActive,
              },
            }
          )
        );

        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "🔄 Subscription Updated",
              description: `User with customer ID ${customerId} updated their subscription.`,
              color: 0x0099ff,
              fields: [
                {
                  name: "New Plan",
                  value: `${planName} (${priceId})`,
                  inline: true,
                },
                {
                  name: "Price",
                  value: `${priceAmount.toFixed(2)} ${priceCurrency}`,
                  inline: true,
                },
                { name: "Is Active", value: String(isActive), inline: true },
                { name: "Customer ID", value: customerId, inline: true },
              ],
              timestamp: new Date().toISOString(),
            },
          ],
        });
      } catch (error) {
        console.error("Error handling customer.subscription.updated:", error);
        return NextResponse.json(
          { error: "Internal Server Error" },
          { status: 500 }
        );
      }
      break;
    }

    case "customer.subscription.deleted": {
      const subscription = event.data.object as Stripe.Subscription;
      const customerId = subscription.customer as string;
      const priceId = subscription.items?.data?.[0]?.price?.id;
      const plan = priceId ? getPlanByPriceId(priceId) : undefined;
      const planName = plan?.name || "Unknown Plan";

      try {
        await retryOperation(() =>
          User.updateOne(
            { customerId },
            {
              $set: {
                hasAccess: false,
                priceId: null,
              },
            }
          )
        );

        await DISCORD_WEBHOOK.send({
          embeds: [
            {
              title: "🚫 Subscription Canceled",
              description: `User with customer ID ${customerId} canceled their subscription.`,
              color: 0xffcc00,
              fields: [
                { name: "Canceled Plan", value: planName, inline: true },
                { name: "Customer ID", value: customerId, inline: true },
              ],
              timestamp: new Date().toISOString(),
            },
          ],
        });
      } catch (error) {
        console.error("Error handling customer.subscription.deleted:", error);
        return NextResponse.json(
          { error: "Internal Server Error" },
          { status: 500 }
        );
      }
      break;
    }

    case "invoice.payment_failed": {
      const invoice = event.data.object as Stripe.Invoice;
      const customerId = invoice.customer as string;
      const customerEmail = invoice.customer_email;
      const priceId = invoice.lines.data[0]?.price?.id;
      const plan = priceId ? getPlanByPriceId(priceId) : undefined;
      const planName = plan?.name || "Unknown Plan";

      await DISCORD_WEBHOOK.send({
        embeds: [
          {
            title: "⚠️ Payment Failed",
            description: `A payment from ${
              customerEmail ? customerEmail : `customer ${customerId}`
            } failed. We are NOT handling this or revoking their access`,
            color: 0xffa500,
            fields: [
              {
                name: "Amount Due",
                value: `${(invoice.amount_due / 100).toFixed(2)} ${
                  invoice.currency
                }`,
                inline: true,
              },
              {
                name: "Plan",
                value: planName,
                inline: true,
              },
              {
                name: "Customer",
                value: customerEmail
                  ? `||${customerEmail}||`
                  : `ID: ${customerId}`,
                inline: true,
              },
            ],
            timestamp: new Date().toISOString(),
          },
        ],
      });
      break;
    }

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({});
};

export const POST = wrapApiWithManagerAndContext(handlePost, {
  accessLevel: "public",
  discordErrorReporting: true,
});
