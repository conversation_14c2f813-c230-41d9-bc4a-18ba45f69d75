@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  scroll-behavior: smooth !important;
}

/* It makes the HTML progress bar filling smooth when value change. */
progress::-webkit-progress-value {
  transition: 0.6s width ease-out;
}

@layer components {
  .basic-animate {
    @apply transition-all duration-300 ease-in-out;
  }
  .raise-on-hover {
    @apply basic-animate hover:-translate-y-1 hover:shadow-lg;
  }
}

@layer base {
  .btn-gradient {
    @apply !bg-gradient !bg-[length:300%_300%] hover:saturate-[1.2] shadow duration-100 !border-0 !border-transparent !bg-transparent animate-shimmer disabled:!bg-none disabled:!bg-muted/30  !text-primary-foreground;
  }
  .btn {
    @apply !capitalize;
  }
  /**
  * Professional AI automation platform color scheme
  * Primary: Deep professional blue (#2563eb) - conveys trust, intelligence, and reliability
  * Accents: Complementary colors that suggest innovation and efficiency
  **/

  :root {
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;
    --primary: 221 83% 53%;  /* #2563eb - Professional blue */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 94%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    --chart-1: 221 83% 53%;  /* Primary blue */
    --chart-2: 262 83% 58%;  /* Purple accent */
    --chart-3: 142 76% 36%;  /* Success green */
    --chart-4: 346 87% 43%;  /* Warning red */
    --chart-5: 204 94% 94%;  /* Light blue */
    --radius: 0.5rem;
    --sidebar-background: 210 40% 98%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 94%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 221 83% 53%;
  }
  
  .dark {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;  /* #3b82f6 - Lighter blue for dark mode */
    --primary-foreground: 222 84% 4.9%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 217 91% 60%;
    --chart-1: 217 91% 60%;  /* Primary blue */
    --chart-2: 262 83% 68%;  /* Purple accent */
    --chart-3: 142 76% 46%;  /* Success green */
    --chart-4: 346 87% 53%;  /* Warning red */
    --chart-5: 217 32% 17%;  /* Dark blue */
    --sidebar-background: 222 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 222 84% 4.9%;
    --sidebar-accent: 217 32% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 32% 17%;
    --sidebar-ring: 217 91% 60%;
  }
  
}

@layer base {
  html {
    @apply min-h-screen bg-background;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-screen;
  }
}
