import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import config from "@/config";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// CHATGPT PROMPT TO GENERATE YOUR TERMS & SERVICES — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.
// Write a **concise, legally sound** Terms of Service for my website.

// Here is the required context:
// - **Website**: https://dothistask.ai
// - **Name**: DoThisTaskAI
// - **Contact**: <EMAIL>
// - **Description**: A process identification and solution generation platform meant to connect knowledge workers with AI solutions that help them automate time consuming and repetitive processes at their jobs.
// - **Ownership & Data Export**:
//   - Users own the AI solutions they generate through the platform.
//   - Premium and enterprise users may mark their AI solutions as "private" to prevent public sharing.
//   - Enterprise customers may access their organization's usage data and generated solutions.
//   - Users may request export of their data.
// - **User Data Collected**: Name, email, role at company, job process data, task definitions, AI solution usage data.
// - **Non-Personal Data**: Web cookies, usage analytics, search queries.
// - **Disputes & Governing Law**: USA.
// - **Updates**: Users will be notified via email.
// - **Current Date**: 07/06/2025

// Ensure that the document is **concise and clear**, with proper legal enforceability. **Do not explain or add extra details.**

// Turn this document into code similar to the following component:

const TERMS_OF_SERVICE_CONTENT = `# Terms of Service for DoThisTaskAI

**Last Updated: July 6, 2025**

Welcome to DoThisTaskAI!

These Terms of Service ("Terms") govern your use of the DoThisTaskAI website at https://dothistask.ai ("Website") and the services provided by DoThisTaskAI ("we," "us," or "our"). By using our Website and services, you agree to these Terms.

## 1. Description of DoThisTaskAI

DoThisTaskAI is a process identification and solution generation platform designed to connect knowledge workers with AI solutions that help them automate time-consuming and repetitive processes at their jobs. Our platform provides personalized AI-powered solutions to streamline workflows and improve productivity.

## 2. Ownership and Usage Rights

### 2.1 User-Generated Content
- **You own the AI solutions you generate** through our platform, including all intellectual property rights to the solutions you create.
- **Premium and enterprise users** may mark their AI solutions as "private" to prevent them from being publicly shared with other users.
- **Enterprise customers** may access their organization's usage data and generated solutions within their account dashboard.

### 2.2 Data Export
- Users may request export of their data, including generated solutions and usage analytics.
- Data export requests can be made by contacting <NAME_EMAIL>.

### 2.3 Platform License
- We grant you a limited, non-exclusive, non-transferable license to use our platform in accordance with these Terms.
- You may not resell, redistribute, or sublicense access to our platform without our express written consent.

## 3. User Data and Privacy

We collect and store user data to provide our services, including:

### 3.1 Personal Data
- Name and email address
- Role at company
- Job process data and task definitions
- AI solution usage data

### 3.2 Non-Personal Data
- Web cookies for session management
- Usage analytics, search queries, and user interaction data
- Platform performance metrics and error tracking
- User behavior analytics including page views, clicks, and feature usage
- A/B testing and feature experimentation data
- Session tracking and user journey analytics

For complete details on how we handle your data, please refer to our Privacy Policy at https://dothistask.ai/privacy-policy.

## 4. User Responsibilities

By using our platform, you agree to:
- Provide accurate and complete information when creating your account
- Use the platform only for lawful purposes
- Not attempt to circumvent any security measures
- Respect the intellectual property rights of others
- Not use the platform to generate solutions that violate applicable laws or regulations
- Consent to the collection and use of analytics data as described in our Privacy Policy for service improvement and optimization

## 5. Service Availability

- We strive to maintain high availability of our services but cannot guarantee uninterrupted access.
- We reserve the right to suspend or terminate services for maintenance, updates, or technical issues.
- We may modify or discontinue features with reasonable notice to users.

## 6. Limitation of Liability

To the fullest extent permitted by law:
- DoThisTaskAI shall not be liable for any indirect, incidental, special, or consequential damages
- Our total liability shall not exceed the amount paid by you for our services in the 12 months preceding the claim
- We provide the platform "as is" without warranties of any kind

## 7. Termination

- Either party may terminate these Terms at any time with or without cause
- Upon termination, your access to the platform will cease, but you retain ownership of your generated solutions
- We may retain certain data as required by law or for legitimate business purposes

## 8. Governing Law and Disputes

- These Terms are governed by the laws of the United States of America
- Any disputes arising from these Terms shall be resolved in the courts of the United States
- You agree to resolve disputes through binding arbitration where permitted by law

## 9. Updates to Terms

We may update these Terms from time to time to reflect changes in our services or legal requirements. When we make significant changes:
- Users will be notified via email
- Continued use of the platform after notification constitutes acceptance of the updated Terms

## 10. Contact Information

For any questions or concerns regarding these Terms of Service, please contact us at:

**Email**: <EMAIL>

Thank you for using DoThisTaskAI!`;

export const metadata = getSEOTags({
  title: `Terms and Conditions | ${config.metadata.appName}`,
  canonicalUrlRelative: "/tos",
});

const TOS = () => {
  return (
    <main className="max-w-xl mx-auto bg-background text-foreground">
      <div className="p-5">
        <Link
          href="/"
          className="inline-flex items-center gap-2 px-3 py-2 rounded-md text-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
              clipRule="evenodd"
            />
          </svg>
          Back
        </Link>
        <h1 className="text-3xl font-extrabold pb-6 text-foreground">
          Terms and Conditions for {config.metadata.appName}
        </h1>

        <div className="prose prose-sm max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              h1: ({ children }) => (
                <h1 className="text-2xl font-bold mb-4 mt-6 text-foreground">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-xl font-semibold mb-3 mt-6 text-foreground">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-lg font-medium mb-2 mt-4 text-foreground">
                  {children}
                </h3>
              ),
              p: ({ children }) => (
                <p className="mb-3 leading-relaxed text-foreground">
                  {children}
                </p>
              ),
              ul: ({ children }) => (
                <ul className="list-disc ml-6 mb-3 space-y-1 text-foreground">
                  {children}
                </ul>
              ),
              li: ({ children }) => (
                <li className="leading-relaxed text-foreground">{children}</li>
              ),
              strong: ({ children }) => (
                <strong className="font-semibold text-foreground">
                  {children}
                </strong>
              ),
              a: ({ children, href, ...props }) => (
                <a
                  href={href}
                  className="text-primary hover:text-primary/80 underline transition-colors"
                  {...props}
                >
                  {children}
                </a>
              ),
            }}
          >
            {TERMS_OF_SERVICE_CONTENT}
          </ReactMarkdown>
        </div>
      </div>
    </main>
  );
};

export default TOS;
