import { Suspense } from "react";
import Header from "@/components/Header";
import Hero from "@/components/landing-page/Hero";
import Problem from "@/components/landing-page/Problem";
import FeaturesAccordion from "@/components/landing-page/FeaturesAccordion";
import Pricing from "@/components/landing-page/Pricing";
import FAQ from "@/components/landing-page/FAQ";
import CTA from "@/components/landing-page/CTA";
import Footer from "@/components/Footer";
import env from "@/libs/env";
import HeaderPreLaunch from "@/components/HeaderPreLaunch";
import { getPricingData } from "@/libs/stripe";

export default async function LandingPage() {
  const stripePrices = await getPricingData();
  return (
    <>
      <Suspense>
        {env.WAITLIST_MODE_FLAG ? <HeaderPreLaunch /> : <Header />}
      </Suspense>
      <main>
        <Hero />
        <Problem />
        <FeaturesAccordion />
        {!env.WAITLIST_MODE_FLAG && <Pricing stripePrices={stripePrices} />}
        <FAQ />
        <CTA />
      </main>
      <Footer />
    </>
  );
}
