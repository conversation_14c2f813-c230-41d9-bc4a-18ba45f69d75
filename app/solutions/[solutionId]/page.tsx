import { SolutionPage } from "@/components/pages/solution-page";
import { getSolutionSEOTags, getSolutionStructuredData } from "@/libs/seo";
import Script from "next/script";
import AppHeader from "@/components/app-header";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";
import { Breadcrumbs } from "@/components/breadcrumbs";
import { getSolutionById } from "@/lib/database";
import { notFound } from "next/navigation";
import { SolutionItem } from "@/types/models/solution";

export const revalidate = 300;

interface SolutionPageProps {
  params: Promise<{ solutionId: string }>;
}

export async function generateMetadata({ params }: SolutionPageProps) {
  const { solutionId } = await params;

  const solution = await getSolutionById({ id: solutionId });

  if (!solution) {
    return {
      title: "Solution Not Found",
      description: "The requested solution could not be found.",
    };
  }

  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));
  return getSolutionSEOTags(plainSolution);
}

export default async function SolutionPageRoute({ params }: SolutionPageProps) {
  const { solutionId } = await params;

  // Get solution for the component
  const solution = await getSolutionById({ id: solutionId });

  if (!solution) {
    notFound();
  }

  // Fetch user's collections for header
  const collections = await getUserCollectionsServer();
  const plainSolution: SolutionItem = JSON.parse(JSON.stringify(solution));

  const breadcrumbItems: Array<{ label: string; href?: string }> = []; // Solution page has no breadcrumbs

  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        type="application/ld+json"
        id={`json-ld-solution-${solutionId}`}
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getSolutionStructuredData(plainSolution)),
        }}
      />

      <AppHeader collections={collections} />

      <div className="container mx-auto p-6">
        <Breadcrumbs items={breadcrumbItems} />
        <SolutionPage solution={plainSolution} />
      </div>
    </>
  );
}
