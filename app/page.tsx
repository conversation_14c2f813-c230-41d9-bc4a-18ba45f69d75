import { HomePageHybrid } from "@/components/home-page-hybrid";
import { executeUnifiedSearch } from "@/lib/search-service";
import { getRoles } from "@/lib/database";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";

export const revalidate = 300;

export default async function HomePage() {
  // Fetch initial data on server
  const [roles, initialTasks, collections] = await Promise.all([
    getRoles(),
    executeUnifiedSearch({
      query: "", // Browse mode
      limit: 20,
      minScore: 0.3,
    }),
    getUserCollectionsServer(),
  ]);

  return (
    <HomePageHybrid
      initialRoles={roles}
      initialTasks={initialTasks}
      collections={collections}
    />
  );
}
