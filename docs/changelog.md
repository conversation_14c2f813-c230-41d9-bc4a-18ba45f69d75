# Changelog

All notable changes to DoThisTaskAI will be documented in this file.

The format follows the guidelines from main.mdc:
- [CHANGE] entries with Added, Changed, Fixed sections
- Include ticket/issue numbers when applicable
- Reference related previous work or dependencies
- add at the top

---

## [Default Collection for New Users] - 2025-07-08

### Added
- **Automatic Default Collection Creation**: Every new user now gets a default collection automatically created upon registration
  - Default collection named "My Favorites" with star icon and blue color
  - Created via NextAuth `events.createUser` callback when new users are created
  - Includes descriptive subtitle: "Your personal collection of favorite tasks and solutions"
  - Prevents duplicate creation by checking existing collection count before creating
- **Server-Side Collection Utility**: New `createDefaultCollectionForUser()` function in `lib/server-collection-utils.ts`
  - Takes user ID as parameter and creates default collection with pre-configured settings
  - Includes error handling to prevent disrupting user creation process
  - Logs successful creation and errors for monitoring
- **Enhanced User Creation Tracking**: Improved Discord notifications to distinguish between user creation and login
  - Separate notification for new user creation vs. user login
  - User creation notification includes user ID for tracking purposes
  - Different colors for creation (blue) vs. login (green) notifications

### Changed
- **NextAuth Configuration**: Enhanced authentication flow to handle new user onboarding
  - Added `events.createUser` callback to trigger default collection creation
  - Separated user creation tracking from login tracking for better analytics
  - Error handling ensures user creation process continues even if collection creation fails

### Technical Details
- Uses MongoDB collection count check to prevent duplicate default collections
- Integration with existing collection creation infrastructure via Collection model
- Default collection uses same validation and structure as user-created collections
- Non-blocking implementation ensures authentication flow remains fast and reliable
- Follows project's modular architecture by extending server collection utilities

---

## [2025-07-07] - Major Navigation Refactor

### Added
- New breadcrumbs component for consistent navigation across all pages
- Reusable page components for collections, collection detail, role, and solution pages
- Missing collection solution route: `/collections/[collectionId]/solutions/[solutionId]`
- Proper breadcrumb hierarchy:
  - Collections page: no breadcrumbs
  - Collection detail page: Collections → Collection Name
  - Role page: Roles → Role Name
  - Solution pages with contextual breadcrumbs based on entry point

### Changed
- Refactored all main page routes to use consistent breadcrumb + page component pattern
- Collection detail page now uses configurable solution link patterns
- Solution pages now support different contexts (standalone, role-based, collection-based)
- Eliminated code duplication across page components

### Removed
- Old `collections-page-client.tsx` component (replaced by reusable page component)
- Old `solution-page-client.tsx` component (replaced by reusable page component)
- Embedded UI components from page.tsx files (extracted to separate components)

### Technical
- Created `components/breadcrumbs.tsx` for consistent navigation
- Created `components/pages/` directory with reusable page components
- Updated all page routes to follow consistent pattern: breadcrumbs + page component
- Improved TypeScript typing for breadcrumb items and page props

---

## [Usage Tracking System] - 2025-07-04

### Added
- **Solution Usage Tracking**: Comprehensive tracking system for run and copy button interactions
  - Added `usageCount` field to Solution model with default value 0
  - Created `trackSolutionUsage` endpoint in analytics router following same pattern as view tracking
  - Implemented 10-minute cache deduplication using Redis to prevent duplicate counts
  - Added rate limiting (50 requests per 10 minutes) for usage tracking endpoint
- **Usage Tracking Hook**: Created `hooks/use-track-solution-usage.tsx` for client-side usage tracking
  - Generates client-side fingerprint for enhanced deduplication
  - Provides `trackUsage` function for manual usage tracking
  - Includes loading state and error handling
- **Button Integration**: Updated all copy and run buttons to track usage
  - Enhanced `AIPromptButton` with `onRunClick` callback prop
  - Enhanced `CopyButton` with `onCopyClick` callback prop (tracks all copy actions)
  - Enhanced `SimpleCopyButton` with `onCopyClick` callback prop
  - Updated `PromptCard` to accept `solutionId` and coordinate usage tracking
  - Updated `PromptSheet` component to track usage from copy buttons in modal

### Changed
- **Component Props**: Updated button components to accept optional callback props
  - `AIPromptButton`: Added `onRunClick?: () => void` prop
  - `CopyButton`: Added `onCopyClick?: () => void` prop
  - `SimpleCopyButton`: Added `onCopyClick?: () => void` prop
  - `PromptCard`: Added required `solutionId: string` prop
  - `PromptSheet`: Added required `solutionId: string` prop
- **Analytics Architecture**: Extended existing view tracking system for usage tracking
  - Uses same Redis caching strategy with 10-minute TTL
  - Uses same fingerprinting approach for user deduplication
  - Maintains consistency with existing analytics patterns

### Technical Details
- Usage tracking follows same pattern as view tracking with dedicated Redis keys
- Atomic increment operations (`$inc`) prevent race conditions in MongoDB updates
- Client-side fingerprinting includes language, screen dimensions, and timezone
- Button callbacks execute usage tracking before existing functionality
- Type-safe implementation with proper TypeScript interfaces throughout
- Maintains backwards compatibility with existing button implementations

---

## [Home Page Architecture Simplification] - 2025-07-03

### Changed
- **Unified Search API**: Consolidated `trpc.search.searchTasks` and `trpc.content.getTasks` into single `trpc.search.searchTasks` endpoint
  - Single API handles both search (Pinecone) and browse (MongoDB) modes based on query presence
  - Simplified response structure: `{task, solution, searchMetadata}` instead of merged task/search objects
  - Automatic mode switching: empty query uses MongoDB, non-empty query uses Pinecone
  - Unified infinite scroll implementation across both modes
- **Combined Search Component**: Merged `HomeSearch` and `HomeHeroBadges` into single `HomeSearchWithFilters` component
  - Reduced component count from 3 to 2 for the home page
  - Integrated search input and role filtering in one cohesive component
  - Simplified prop passing and state management
- **Simplified Page Client**: Reduced `HomePageClient` component complexity
  - Removed role toggle logic (now handled in combined component)
  - Simplified state management with fewer state variables
  - Cleaner component architecture with better separation of concerns

### Removed
- **Deprecated Components**: Removed `HomeSearch.tsx` and `HomeHeroBadges.tsx` (functionality merged)
- **Duplicate Logic**: Eliminated duplicate infinite scroll and data fetching logic
- **Complex State Management**: Removed redundant state synchronization between components

### Technical Details
- Backend now returns structured response with task, solution, and searchMetadata objects
- Frontend uses single API call for all data needs instead of multiple parallel queries
- Improved type safety with consistent data structures throughout the stack
- Enhanced performance with reduced component renders and API calls
- Maintains all existing functionality while simplifying the codebase

### Added
- **Skeleton Loading Indicator**: Added skeleton loading card when fetching the next page during infinite scroll
  - Visual feedback shows loading state while more results are being fetched
  - Skeleton matches the actual card design for seamless visual continuity
  - Improves user experience during pagination loading

### Fixed
- **Role Filtering Bug in Browse Mode**: Fixed critical issue where role filters weren't working in MongoDB browse mode
  - Role IDs were being passed as strings but MongoDB was expecting ObjectIds for the `roles` field
  - Fixed `getTasksLimited` function to convert role filter strings to ObjectIds before MongoDB query
  - Fixed `getTotalTaskCount` function to convert role filter strings to ObjectIds for accurate count
  - Browse mode with role filters now correctly displays only tasks matching the selected roles
- **Type System Cleanup**: Moved response schemas to shared types file and eliminated duplicate type definitions
  - Moved `unifiedSearchResultSchema` to `types/models/search.ts` for shared use
  - Added proper input/output validation to backend API using shared schemas
  - Removed duplicate `SearchResult` interfaces across frontend components
  - All components now use `UnifiedSearchResult` type from common types file
  - Enhanced type safety with consistent schema validation throughout the stack
- **Solution Card Design**: Improved solution cards with clickable navigation to solution pages
  - Fixed type compatibility issues with solution display components
  - Enhanced card layout with better visual hierarchy and action buttons
  - Fixed linter errors in home results section

---

## [Copy Modal Button State Fix] - 2025-07-02

### Fixed
- **Go to Platform Button**: Fixed issue where "Go to Platform" button would re-disable after 2 seconds
  - Added separate `hasCopied` state to track if user has copied during modal session
  - Button remains enabled permanently after copying until modal closes
  - Copy button visual feedback (`copied` state) still resets after 2 seconds as expected
  - Modal state resets when reopened or closed

### Technical Details
- Separated visual feedback state (`copied`) from functional state (`hasCopied`)
- `hasCopied` persists throughout modal session and only resets on modal close/reopen
- Maintains existing copy button UX while fixing platform navigation button behavior

---

## [AI Platforms Update & Copy Enforced Modal] - 2025-07-02

### Added
- **Gemini Platform Support**: Added Google Gemini to AI_PLATFORMS with `copyEnforced` attribute and URL
- **Enhanced Copy Enforced Modal**: New shadcn dialog for platforms that don't support direct linking
  - Shows explanatory message about manual copy requirement
  - Copy button with success feedback for both regular and interactive prompts
  - **"Go to Platform" button**: Enabled after successful copy, opens platform in new tab
  - Professional design using shadcn Dialog components and color scheme from globals.css
  - Responsive layout with proper button sizing and spacing
- **Visual Indicators**: Copy-enforced platforms show "(Copy)" label in dropdown
- **Type Guards**: Added `isCopyEnforced()` helper function for type safety

### Changed
- **Updated Platform URLs**: 
  - ChatGPT: `chat.openai.com` → `chatgpt.com`
  - Claude: `claude.ai/chat` → `claude.ai/new`
  - Perplexity: `www.perplexity.ai` → `perplexity.ai`
- **Enhanced Type System**: Copy-enforced platforms now include URLs for "go to platform" functionality
- **Modal Architecture**: Replaced custom modal with shadcn Dialog components for better accessibility
- **User Experience**: Enhanced workflow - copy prompt first, then navigate to platform

### Technical Details
- Uses TypeScript discriminating unions with both platform types requiring URLs
- Modal implementation uses shadcn Dialog with proper semantic structure
- Copy functionality includes automatic timeout for success state
- "Go to Platform" button disabled until copy is successful
- Follows globals.css color scheme for consistent theming
- Type-safe implementation with proper null checking and optional chaining

---

## [Enhanced SEO Configuration] - 2025-07-02

### Changed
- Enhanced SEO configuration in `libs/seo.tsx` to better align with DoThisTaskAI's purpose as a process identification and solution generation platform
- Updated default keywords to include AI automation, process identification, knowledge workers, and workflow optimization terms
- Improved structured data with accurate business application categories and process automation subcategories
- Enhanced role and solution SEO functions with more targeted, automation-focused keywords
- Updated page titles to be more descriptive (e.g., "Role AI Solutions", "Solution - AI Solution")

### Added
- Comprehensive freemium business model representation in structured data with Free, Pro, and Enterprise tier pricing
- Enhanced audience targeting in structured data specifically for knowledge workers
- New `getOrganizationStructuredData()` function for better organization representation
- Feature lists in structured data highlighting AI-powered process identification and custom solution generation
- Better service descriptions aligned with the platform's core modules (directory, dynamic solution, process identification)
- Enhanced breadcrumb structures and "about" sections for better semantic understanding

### Technical Details
- Added DEFAULT_KEYWORDS array with DoThisTaskAI-specific terms
- Updated application categories from "EducationalApplication" to "BusinessApplication" with "Process Automation" subcategory
- Improved meta descriptions to emphasize automation and productivity benefits
- Enhanced structured data for better search engine understanding of the platform's purpose

## [Total Count Display] - 2025-07-01

### Added
- **Total Count Display**: Shows the total number of available results alongside currently loaded results
- **Accurate Counts**: Separate total counts for search results vs browse results with role filtering
- **Progress Indicators**: Shows "showing X of Y total" when more results are available
- **Real-time Updates**: Total counts update automatically when role filters change

### Technical Details
- Added `getTotalTaskCount` function to database layer for efficient counting with filters
- Enhanced search and content routers to return total counts alongside paginated results
- Updated frontend components to display and track total counts separately from loaded counts
- Search total count reflects actual matching results after similarity threshold filtering

## [Infinite Query Implementation] - 2025-07-01

### Added
- **Infinite Scroll**: Automatic loading of more results as user scrolls to bottom
- **Cursor-based Pagination**: More efficient pagination using document IDs instead of offset
- **Loading Indicators**: Skeleton animations during infinite loading for better UX
- **Search Infinite Query**: Search results now load infinitely with scroll detection

### Changed
- Replaced "Load More" buttons with seamless infinite scroll for both search and browse modes
- Updated all queries to use `useInfiniteQuery` from tRPC instead of regular queries with offset pagination
- Modified backend endpoints to return `nextCursor` and `hasNextPage` instead of `offset` and `hasMore`
- Simplified component props by removing pagination-related state management
- Enhanced scroll detection with 200px threshold for smooth loading experience

### Fixed
- Removed complex pagination state management that was causing unnecessary re-renders
- Improved performance by eliminating manual offset tracking and result appending logic

### Technical Details
- Search uses cursor-based filtering of vector search results for pagination simulation
- Browse mode uses MongoDB `$lt` cursor queries with `_id` field for true cursor pagination
- Infinite scroll triggers when user reaches within 200px of document bottom
- Automatic query refetching when role filters change
- Type-safe implementation throughout with proper TypeScript interfaces

---

## [Semantic Search Implementation] - 2025-07-01

### Added
- **Semantic Search System**: Full-featured search functionality using OpenAI embeddings and Pinecone vector database
  - `libs/search.ts`: Core search logic with OpenAI text-embedding-3-small model integration
  - `server/routers/search.ts`: tRPC router with `searchTasks` and `healthCheck` procedures
  - `types/models/search.ts`: Comprehensive type definitions for search inputs and outputs
  - Advanced search features: similarity scoring, role filtering, configurable thresholds
  - Health monitoring for both OpenAI and Pinecone services
- **Interactive Homepage**: Complete redesign of main page with search functionality
  - `components/home-search.tsx`: Debounced search input with real-time results
  - `components/home-page-client.tsx`: State management for search and role filtering
  - Role-based filtering with visual selection indicators
  - Search result highlighting with similarity percentages
  - Clear search functionality with smooth state transitions
- **Enhanced Components**: Updated existing components to support search
  - Modified `HomeResultsSection` to display both search results and default tasks
  - Updated `HomeHeroBadges` to support role filtering instead of navigation
  - Added search status indicators and performance metrics display
- **Rate Limiting**: Implemented search-specific rate limits (20 requests/60 seconds)
- **Custom Hook**: Created `hooks/use-debounce.tsx` for optimized search performance

### Changed
- Homepage now uses client-side architecture for interactive search while maintaining SEO benefits
- Role badges in hero section now filter results instead of navigating to role pages
- Task results display includes similarity scores for search results
- Results section adapts header and description based on search state
- **Enhanced Role Filtering**: Role badges now work as persistent filters for both search and browse modes
  - Role filters apply to both search results and general task browsing
  - Visual improvements: checkmarks for selected roles, filter count display
  - Backend support for role-based filtering in browse mode via `getTasks` endpoint
  - Clear messaging when role filters are active

### Technical Details
- Uses existing Pinecone vector database from previous sync implementation
- Leverages OpenAI text-embedding-3-small model for query embeddings
- Implements configurable similarity thresholds (default: 0.6)
- Supports role-based filtering with multiple role selection
- Includes comprehensive error handling and performance monitoring
- Built with TypeScript for full type safety across search pipeline

---

## [Pinecone Vector Database Integration] - 2025-06-30

### Added
- Task model field `mostRecentSyncToVectorDatabase` for tracking sync state
- Pinecone package dependency (`@pinecone-database/pinecone`)
- Environment variables for Pinecone configuration (`PINECONE_API_KEY`, `PINECONE_INDEX_NAME`)
- Vector database sync script (`scripts/sync-tasks-to-pinecone.ts`) with comprehensive features:
  - **OpenAI Integration**: Uses official OpenAI package instead of raw HTTP requests
  - **Automatic Index Creation**: Creates Pinecone index if it doesn't exist with proper configuration
  - **Index Readiness Checking**: Waits for index to be fully ready before proceeding
  - **Batched Embedding Generation**: Processes up to 1000 tasks per API call (reduces calls by ~99%)
  - **Rate Limit Management**: Built-in delays and error handling for API rate limits
  - Intelligent sync logic (only syncs new/updated tasks)
  - Batch processing for efficient upserts (100 records per batch)
  - Rich metadata storage for advanced filtering and search
  - Comprehensive error handling and logging
  - Database timestamp updates after successful sync
- New npm script: `pnpm run sync-tasks`
- Complete setup documentation (`temp/pinecone-sync-setup.md`)
- Namespace organization using 'tasks' namespace for data separation

### Changed
- Extended environment configuration schema to include Pinecone settings
- Enhanced Task model with vector database sync tracking capabilities
- Improved embedding generation using OpenAI's official client for better reliability

### Technical Details
- Index configuration: text-embedding-3-small (1536 dimensions), cosine similarity, serverless AWS
- Vector content: `taskName + " " + taskDescription`
- Automatic index creation with readiness verification
- Robust error handling and progress logging

## [CLI Import Tool] - 2025-06-30

### Added
- CLI tool for importing tasks from JSON files (`scripts/import-tasks.ts`)
- OpenAI o3 integration for automated prompt generation (`sendOpenAiO3` function in `libs/gpt.ts`)
- Comprehensive task validation using Zod schemas
- Automatic creation of Task and Solution database entries
- Rate limiting protection for OpenAI API calls
- Dry-run mode for testing without database changes
- Sample task JSON file and detailed usage documentation
- New npm script: `pnpm run import-tasks`

### Changed
- Updated OpenAI integration to use official `openai` package instead of raw HTTP requests
- Enhanced error handling and logging throughout the import process


## [Initial Setup] - 2025-06-29

### Added
- Project scaffolding with Next.js App Router
- Authentication system with NextAuth
- Database integration with MongoDB/Mongoose
- TRPC setup for type-safe API routes
- Shadcn UI components and Tailwind CSS styling
- Basic landing page structure
- Blog functionality with articles and authors
- Onboarding wizard component structure
- User roles and permissions system
- Stripe integration for payments
- Basic sidebar navigation
- Core project documentation (app-info.md, feature_roadmap.md, changelog.md)

### Fixed
- Initial project configuration and dependencies
