import themes from "daisyui/src/theming/themes";
import type { ConfigProps } from "./types/config";

const config: ConfigProps = {
  metadata: {
    // REQUIRED
    appName: "DoThisTaskAI",
    // REQUIRED: a short description of your app for SEO tags (can be overwritten)
    appDescription:
      "A process identification and solution generation platform meant to connect knowledge workers with AI solutions that help them automate time consuming and repetitive processes at their jobs.",
    // REQUIRED (no https://, not trialing slash at the end, just the naked domain)
    domainName: "dothistask.ai",
    creator: {
      twitterHandle: "N3sOnline",
      name: "N3<PERSON>",
      description:
        "<PERSON> is a solo entrepreneur focused on building cool tools online.",
    },
  },
  crisp: {
    // Crisp website ID. IF YOU DON'T USE CRISP: just remove this => Then add a support email in this config file (resend.supportEmail) otherwise customer support won't work.
    id: "",
    // Hide Crisp by default, except on route "/". Crisp is toggled with <ButtonSupport/>. If you want to show <PERSON>risp on every routes, just remove this below
    onlyShowOnRoutes: ["/"],
  },
  aws: {
    // If you use AWS S3/Cloudfront, put values in here
    bucket: "bucket-name",
    bucketUrl: `https://bucket-name.s3.amazonaws.com/`,
    cdn: "https://cdn-id.cloudfront.net/",
  },
  resend: {
    // REQUIRED — Email 'From' field to be used when sending magic login links
    fromNoReply: `DoThisTaskAI <<EMAIL>>`,
    // REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..
    fromAdmin: `Support at DoThisTaskAI <<EMAIL>>`,
    // Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support."
    supportEmail: "<EMAIL>",
  },
  colors: {
    // REQUIRED — The DaisyUI theme to use (added to the main layout.js). Leave blank for default (light & dark mode). If you any other theme than light/dark, you need to add it in config.tailwind.js in daisyui.themes.
    theme: "light",
    // REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..). By default it takes the primary color from your DaisyUI theme (make sure to update your the theme name after "data-theme=")
    // OR you can just do this to use a custom color: main: "#f37055". HEX only.
    main: themes["light"]["primary"] || "#4a00ff",
  },
  auth: {
    // REQUIRED — the path to log in users. It's use to protect private routes (like /dashboard). It's used in apiClient (/libs/api.js) upon 401 errors from our API
    loginUrl: "/auth/signin",
    // REQUIRED — the path you want to redirect users after successfull login (i.e. /dashboard, /private). This is normally a private page for users to manage their accounts. It's used in apiClient (/libs/api.js) upon 401 errors from our API & in ButtonSignin.js
    callbackUrl: "/",
  },
};

export default config;
