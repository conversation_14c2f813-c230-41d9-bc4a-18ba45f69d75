import { cache } from "react";
import connectMongo from "@/libs/mongoose";
import { cache as redisCache } from "@/utils/redis/cache";
import Role from "@/models/Role";
import Task from "@/models/Task";
import Solution from "@/models/Solution";
import { roleMongooseParser } from "@/types/models/role";
import { taskMongooseParser } from "@/types/models/task";
import { solutionMongooseParser } from "@/types/models/solution";
import { createRoleSlug } from "./constants";
import mongoose from "mongoose";

export const getRoles = cache(async () => {
  return await redisCache.getOrSet(
    "all-roles",
    async () => {
      await connectMongo();
      const roles = await Role.find({}).sort({ roleName: 1 });
      return roles.map((role) => roleMongooseParser.parse(role));
    },
    {
      ttl: 600, // 10 minutes (roles change infrequently)
      prefix: "roles",
    }
  );
});

export const getTasks = cache(async () => {
  await connectMongo();
  const tasks = await Task.find({}).populate("roles");
  return tasks.map((task) => taskMongooseParser.parse(task));
});

function createTasksCacheKey(params: {
  limit?: number;
  cursor?: string;
  roleFilter?: string[];
}): string {
  const { limit = 20, cursor, roleFilter } = params;
  const parts = [
    `l:${limit}`,
    `c:${cursor || "none"}`,
    `rf:${roleFilter?.sort().join(",") || "none"}`,
  ];
  return parts.join("|");
}

export const getTasksLimited = cache(
  async ({
    limit = 20,
    cursor,
    roleFilter,
  }: { limit?: number; cursor?: string; roleFilter?: string[] } = {}) => {
    // For cursor-based pagination, don't cache as it's user-specific navigation
    if (cursor) {
      return await getTasksLimitedInternal({ limit, cursor, roleFilter });
    }

    // Cache first-page results
    const cacheKey = createTasksCacheKey({ limit, cursor, roleFilter });
    return await redisCache.getOrSet(
      cacheKey,
      () => getTasksLimitedInternal({ limit, cursor, roleFilter }),
      {
        ttl: 300, // 5 minutes
        prefix: "tasks",
      }
    );
  }
);

async function getTasksLimitedInternal({
  limit = 20,
  cursor,
  roleFilter,
}: { limit?: number; cursor?: string; roleFilter?: string[] } = {}) {
  await connectMongo();

  // Build match stage for aggregation
  const matchStage: Record<string, unknown> = {};
  if (roleFilter && roleFilter.length > 0) {
    matchStage.roles = {
      $in: roleFilter.map((id) => new mongoose.Types.ObjectId(id)),
    };
  }

  // Add cursor-based pagination
  if (cursor) {
    matchStage._id = { $lt: new mongoose.Types.ObjectId(cursor) };
  }

  // Use aggregation to join with solutions and sort by view count
  const pipeline: mongoose.PipelineStage[] = [
    { $match: matchStage },

    // Lookup the associated solution to get view count for sorting
    {
      $lookup: {
        from: "solutions",
        localField: "prompts.promptCraftV4",
        foreignField: "_id",
        as: "solution",
      },
    },

    // Add a field for sorting - use solution view count or 0 if no solution
    {
      $addFields: {
        solutionViewCount: {
          $ifNull: [{ $arrayElemAt: ["$solution.viewCount", 0] }, 0],
        },
      },
    },

    // Sort by solution view count (desc), then by task _id (desc) as tiebreaker
    { $sort: { solutionViewCount: -1, _id: -1 } },

    // Remove the temporary fields and solution data from output
    {
      $project: {
        solution: 0,
        solutionViewCount: 0,
      },
    },

    { $limit: limit },
  ];

  const tasks = await Task.aggregate(pipeline);

  // Populate roles for each task
  await Task.populate(tasks, { path: "roles" });

  return {
    tasks: tasks.map((task) => taskMongooseParser.parse(task)),
  };
}

export const getTotalTaskCount = cache(
  async ({ roleFilter }: { roleFilter?: string[] } = {}) => {
    await connectMongo();

    // Build query filter
    const filter: Record<string, unknown> = {};
    if (roleFilter && roleFilter.length > 0) {
      filter.roles = {
        $in: roleFilter.map((id) => new mongoose.Types.ObjectId(id)),
      };
    }

    // Get total count for the filter
    const totalCount = await Task.countDocuments(filter);
    return totalCount;
  }
);

export const getSolutions = cache(async () => {
  await connectMongo();
  const solutions = await Solution.find({}).sort({ viewCount: -1, name: 1 }); // Sort by views (desc), then name (asc) as tiebreaker
  return solutions.map((solution) => solutionMongooseParser.parse(solution));
});

export const getRoleById = cache(async ({ id }: { id: string }) => {
  await connectMongo();
  const role = await Role.findById(id);
  if (!role) return null;
  return roleMongooseParser.parse(role);
});

export const getRoleBySlug = cache(async ({ slug }: { slug: string }) => {
  await connectMongo();
  const roles = await Role.find({});

  // Find role where the slug matches the generated slug from roleName
  const role = roles.find((role) => createRoleSlug(role.roleName) === slug);
  if (!role) return null;
  return roleMongooseParser.parse(role);
});

export const getTasksByRoleId = cache(
  async ({ roleId }: { roleId: string }) => {
    await connectMongo();

    // Use aggregation to join with solutions and sort by view count
    const pipeline: mongoose.PipelineStage[] = [
      { $match: { roles: new mongoose.Types.ObjectId(roleId) } },

      // Lookup the associated solution to get view count for sorting
      {
        $lookup: {
          from: "solutions",
          localField: "prompts.promptCraftV4",
          foreignField: "_id",
          as: "solution",
        },
      },

      // Add a field for sorting - use solution view count or 0 if no solution
      {
        $addFields: {
          solutionViewCount: {
            $ifNull: [{ $arrayElemAt: ["$solution.viewCount", 0] }, 0],
          },
        },
      },

      // Sort by solution view count (desc), then by task _id (desc) as tiebreaker
      { $sort: { solutionViewCount: -1, _id: -1 } },

      // Remove the temporary fields and solution data from output
      {
        $project: {
          solution: 0,
          solutionViewCount: 0,
        },
      },
    ];

    const tasks = await Task.aggregate(pipeline);

    // Populate roles for each task
    await Task.populate(tasks, { path: "roles" });

    return tasks.map((task) => taskMongooseParser.parse(task));
  }
);

export const getTasksByRoleSlug = cache(
  async ({ roleSlug }: { roleSlug: string }) => {
    await connectMongo();
    const role = await getRoleBySlug({ slug: roleSlug });
    if (!role) return [];

    // Use aggregation to join with solutions and sort by view count
    const pipeline: mongoose.PipelineStage[] = [
      { $match: { roles: new mongoose.Types.ObjectId(role.id) } },

      // Lookup the associated solution to get view count for sorting
      {
        $lookup: {
          from: "solutions",
          localField: "prompts.promptCraftV4",
          foreignField: "_id",
          as: "solution",
        },
      },

      // Add a field for sorting - use solution view count or 0 if no solution
      {
        $addFields: {
          solutionViewCount: {
            $ifNull: [{ $arrayElemAt: ["$solution.viewCount", 0] }, 0],
          },
        },
      },

      // Sort by solution view count (desc), then by task _id (desc) as tiebreaker
      { $sort: { solutionViewCount: -1, _id: -1 } },

      // Remove the temporary fields and solution data from output
      {
        $project: {
          solution: 0,
          solutionViewCount: 0,
        },
      },
    ];

    const tasks = await Task.aggregate(pipeline);

    // Populate roles for each task
    await Task.populate(tasks, { path: "roles" });

    return tasks.map((task) => taskMongooseParser.parse(task));
  }
);

export const getSolutionsByTaskId = cache(
  async ({ taskId }: { taskId: string }) => {
    await connectMongo();
    const task = await Task.findById(taskId);
    if (!task || !task.prompts.promptCraftV4) {
      return [];
    }
    const solution = await Solution.findById(task.prompts.promptCraftV4);
    if (!solution) {
      return [];
    }
    return [solutionMongooseParser.parse(solution)];
  }
);

export const getSolutionById = cache(async ({ id }: { id: string }) => {
  await connectMongo();
  const solution = await Solution.findById(id);
  if (!solution) return null;
  return solutionMongooseParser.parse(solution);
});
