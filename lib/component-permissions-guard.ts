import { cache } from "react";
import { getServerSession } from "next-auth";
import User, { IUser } from "@/models/User";
import connectMongo from "@/libs/mongoose";
import { authOptions } from "@/libs/next-auth";
import { MongoDocumentResponse } from "@/types/mongo-helpers";

type AuthorizeProps = {
  hasAccessRequired: boolean;
};

type AuthorizeResponse = {
  user: MongoDocumentResponse<IUser>;
};

export const getUserCached = cache(
  async (userId: string): Promise<MongoDocumentResponse<IUser>> => {
    await connectMongo();

    const user = await User.findOne({ _id: userId });
    if (!user) {
      throw new Error("User not found");
    }

    return user;
  }
);

export const authorizationForServerComponent = async (
  _: AuthorizeProps
): Promise<AuthorizeResponse> => {
  const session = await getServerSession(authOptions);
  if (!session) {
    throw new Error("No session found");
  }

  const user = await getUserCached(session.user.id);

  // if (user.hasAccess === false) {
  //   throw new Error("User does not have access");
  // }

  return { user };
};
