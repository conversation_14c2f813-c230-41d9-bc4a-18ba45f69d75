import { generic<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/libs/backend-errors";
import connectMongo from "@/libs/mongoose";
import { authOptions } from "@/libs/next-auth";
import User, { IUser } from "@/models/User";
import { MongoDocumentResponse } from "@/types/mongo-helpers";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { sendErrorToDiscord } from "./api-discord-middleware";
import { ApiErrorResponse } from "@/types/api-types";

type ApiManagerContext = Partial<{
  user: MongoDocumentResponse<IUser>;
  requestBody: object;
  requestText: string;
}>;

export class ApiContext {
  private request: NextRequest;
  private context: ApiManagerContext;
  constructor(request: NextRequest) {
    this.request = request;
    this.context = {};
  }

  public async getUser(): Promise<MongoDocumentResponse<IUser> | null> {
    if (this.context.user) {
      return this.context.user;
    }
    const session = await getServerSession(authOptions);
    if (!session) {
      return null;
    }
    const { id: userId } = session.user;
    await connectMongo();
    const user = await User.findById(userId);
    if (!user) {
      return null;
    }
    this.context.user = user;
    return user;
  }

  public async getRequestBody(): Promise<object | null> {
    if (this.context.requestBody) {
      return this.context.requestBody;
    }
    const requestData = await this.request
      .clone()
      .json()
      .catch(() => null);

    this.context.requestBody = requestData;
    return requestData;
  }

  public async getRequestText(): Promise<string> {
    if (this.context.requestText) {
      return this.context.requestText;
    }
    const requestText = await this.request.clone().text();
    this.context.requestText = requestText;
    return requestText;
  }

  public async getRequestUrl(): Promise<string> {
    return this.request.url;
  }
}

type ApiManagerConfig = {
  accessLevel: "paid-user" | "logged-in" | "public";
  discordErrorReporting: boolean;
};

const handleHasAccess = async (
  config: ApiManagerConfig,
  context: ApiContext
): Promise<NextResponse<ApiErrorResponse> | null> => {
  if (config.accessLevel === "public") {
    return null;
  }
  const user = await context.getUser();
  if (!user) {
    return NextResponse.json({ errors: ["Unauthorized"] }, { status: 401 });
  }
  if (config.accessLevel === "paid-user" && !user.hasAccess) {
    return NextResponse.json(
      { errors: ["User does not have access"] },
      { status: 403 }
    );
  }
  return null;
};

export type ApiMethod<TApiResponse = unknown> = (
  context: ApiContext
) => Promise<NextResponse<TApiResponse | ApiErrorResponse>>;

export const wrapApiWithManagerAndContext = (
  apiMethod: ApiMethod,
  config: ApiManagerConfig
) => {
  return async (request: NextRequest) => {
    const context = new ApiContext(request);
    try {
      const hasAccessResponse = await handleHasAccess(config, context);
      if (hasAccessResponse) {
        return hasAccessResponse;
      }

      const response = await apiMethod(context);
      if (!response.ok && config.discordErrorReporting) {
        const errorBody = await response
          .clone()
          .json()
          .catch(() => "No JSON body");
        await sendErrorToDiscord(errorBody, request, context.getRequestBody());
      }
      return response;
    } catch (error) {
      if (config.discordErrorReporting) {
        await sendErrorToDiscord(error, request, context.getRequestBody());
      }
      return genericErrorHandler(error);
    }
  };
};
