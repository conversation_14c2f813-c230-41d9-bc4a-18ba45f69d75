export const retryOperation = async <T>(
  operation: () => Promise<T>,
  { retries = 3, delay = 1000 }: { retries?: number; delay?: number } = {}
): Promise<T> => {
  let lastError: Error | undefined;

  for (let i = 0; i < retries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      if (i < retries - 1) {
        // Exponential backoff
        await new Promise((resolve) =>
          setTimeout(resolve, delay * Math.pow(2, i))
        );
      }
    }
  }

  // If all retries fail, throw the last captured error
  throw new Error(
    `Operation failed after ${retries} retries: ${lastError?.message}`
  );
};
