// AI Platform type definitions
type AIPlatformWithUrl = {
  id: string;
  name: string;
  url: string;
};

type AIPlatformWithCopyEnforced = {
  id: string;
  name: string;
  url: string;
  copyEnforced: true;
};

export type AIPlatform = AIPlatformWithUrl | AIPlatformWithCopyEnforced;

// AI Platform configurations
export const AI_PLATFORMS: readonly AIPlatform[] = [
  {
    id: "chatgpt",
    name: "ChatGPT",
    url: "https://chatgpt.com/?q=",
  },
  {
    id: "claude",
    name: "<PERSON>",
    url: "https://claude.ai/new?q=",
  },
  {
    id: "t3chat",
    name: "T3 Chat",
    url: "https://www.t3.chat/new?q=",
  },
  {
    id: "perplexity",
    name: "Perplexity",
    url: "https://perplexity.ai/?q=",
  },
  {
    id: "copilot",
    name: "Copilot",
    url: "https://copilot.microsoft.com/?q=",
  },
  {
    id: "gemini",
    name: "<PERSON>",
    url: "https://gemini.google.com/app",
    copyEnforced: true,
  },
] as const;

// Storage keys for preferences
export const STORAGE_KEY = "preferred-ai-provider";

// Interactive prompt prefix that provides AI guidance for template variables
export const INTERACTIVE_PROMPT_PREFIX = `
# PRE-PROMPT (attach this **before** the Main Prompt)

> **Purpose:** Ensure any placeholder strings in the Main Prompt (e.g., '[industry]', '{{ DATE RANGE }}') are filled with user-supplied information **before** the prompt is executed.  
> **Skip logic:** If the Main Prompt contains **no** bracketed placeholders, ignore this Pre-Prompt and run the Main Prompt as-is.

---

## Step-by-Step Instructions for the AI

1. **Assume the Assigned Role.**  
   - Begin by briefly introducing yourself in the role specified by the Main Prompt (e.g., “As your market research analyst…”).

2. **Begin Information Gathering.**  
   - Politely explain that you need some details to tailor your response.
   - Ask for the first required detail in a natural, conversational way—do **not** reference placeholders or template strings.
   - Provide context and examples to clarify what you’re asking for.

3. **Sequential Input Collection.**  
   - After receiving an answer, move to the next detail needed, again phrased conversationally.
   - If the response is unclear or incomplete, ask for clarification before proceeding.

4. **Acknowledge Completion.**  
   - Once all necessary information has been collected, confirm you have what you need and transition to providing the analysis.

5. **Deliver the Customized Response.**  
   - Present the tailored answer as requested in the Main Prompt.

---

### Key Rules

- **Never mention placeholders or template strings.**
- **Always use role-appropriate, conversational language.**
- **Collect information sequentially, one detail at a time.**
- **Only proceed to analysis after all required information is gathered.**
- **Stay on task.** After delivering the customized response, add no extra commentary unless requested.

---

# MAIN PROMPT 


`;

// Copy preference event name
export const COPY_PREFERENCE_EVENT = "copyPreferenceChanged";

// Utility function to check if a prompt has template variables
export function hasPlaceholders(prompt: string): boolean {
  return /\{\{\s*[^}]+?\s*\}\}/.test(prompt);
}

// Utility function to convert role names to URL-friendly slugs
export function createRoleSlug(roleName: string): string {
  return roleName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
    .trim();
}

export const COLLECTION_LIMIT = 50;
