import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import connectMongo from "@/libs/mongoose";
import Collection from "@/models/Collection";
import { CollectionItem } from "@/types/models/collection";
import { ObjectId } from "mongodb";

/**
 * Fetches user collections on the server side
 * @returns Array of collections or empty array if user is not authenticated
 */
export async function getUserCollectionsServer(): Promise<CollectionItem[]> {
  try {
    // Get the session - this will throw during static generation
    const session = await getServerSession(authOptions);

    // Return empty array if no session
    if (!session?.user?.id) {
      return [];
    }

    // Connect to MongoDB
    await connectMongo();

    // Fetch collections
    const collections = await Collection.find({
      userId: session.user.id,
    })
      .sort({ updatedAt: -1 }) // Most recently updated first
      .lean();

    // Convert to CollectionItem format
    return collections.map((collection) => ({
      id: collection._id.toString(),
      name: collection.name,
      description: collection.description,
      tasks: collection.tasks.map((task: ObjectId) => task.toString()),
      icon: collection.icon,
      color: collection.color,
    }));
  } catch (error) {
    // During static generation, getServerSession will fail because headers() is not available
    // In this case, we just return an empty array to allow static generation to proceed
    if (
      error instanceof Error &&
      error.message.includes("Dynamic server usage")
    ) {
      return [];
    }

    console.error("Error fetching user collections:", error);
    return [];
  }
}

/**
 * Creates a default collection for a new user
 * @param userId - The ObjectId of the user
 * @returns Promise<void>
 */
export async function createDefaultCollectionForUser(
  userId: string
): Promise<void> {
  try {
    await connectMongo();

    // Check if user already has collections to avoid creating duplicates
    const existingCount = await Collection.countDocuments({ userId });

    if (existingCount > 0) {
      return; // User already has collections, skip creating default
    }

    // Create default collection
    await Collection.create({
      name: "My Favorites",
      description: "My personal collection of favorite solutions",
      userId,
      tasks: [],
      isPublic: false,
      icon: "bookmark",
      color: "blue",
    });

    console.log(`Created default collection for user: ${userId}`);
  } catch (error) {
    console.error("Error creating default collection for user:", error);
    // Don't throw the error to prevent disrupting the user creation process
  }
}
