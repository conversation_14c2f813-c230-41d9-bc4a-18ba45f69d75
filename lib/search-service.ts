import { searchTasks } from "@/libs/search";
import { getTasksLimited, getTotalTaskCount } from "@/lib/database";
import connectMongo from "@/libs/mongoose";
import Task from "@/models/Task";
import Solution from "@/models/Solution";
import { taskMongooseParser } from "@/types/models/task";
import { solutionMongooseParser } from "@/types/models/solution";
import type {
  UnifiedSearchInput,
  UnifiedSearchResponse,
  UnifiedSearchResult,
} from "@/types/models/search";
import { cache } from "@/utils/redis/cache";

function createSearchCacheKey(input: UnifiedSearchInput): string {
  const { query, roleFilter, limit, cursor, minScore } = input;
  const parts = [
    `q:${query}`,
    `rf:${roleFilter?.sort().join(",") || "none"}`,
    `l:${limit}`,
    `c:${cursor || "none"}`,
    `ms:${minScore}`,
  ];
  return parts.join("|");
}

async function executeUnifiedSearchInternal(
  input: UnifiedSearchInput
): Promise<UnifiedSearchResponse> {
  const { query, roleFilter, limit, cursor, minScore } = input;
  const hasQuery = query.trim().length > 0;

  let results: UnifiedSearchResult[] = [];
  let nextCursor: string | undefined;
  let hasNextPage = false;
  let totalCount = 0;

  if (hasQuery) {
    // Use Pinecone search for queries
    const searchResults = await searchTasks({
      query,
      limit,
      cursor,
      minScore,
      roleFilter,
    });

    // Get task IDs from search results
    const taskIds = searchResults.results.map(
      (result) => result.metadata.taskId
    );

    if (taskIds.length > 0) {
      await connectMongo();
      const tasks = await Task.find({ _id: { $in: taskIds } }).populate(
        "roles"
      );
      const parsedTasks = tasks.map((task) => taskMongooseParser.parse(task));

      // Get solutions for these tasks
      const solutionIds = parsedTasks
        .map((task) => task.prompts.promptCraftV4)
        .filter(Boolean);

      const solutions = await Solution.find({
        _id: { $in: solutionIds },
      });
      const parsedSolutions = solutions.map((solution) =>
        solutionMongooseParser.parse(solution)
      );

      // Merge results with search metadata
      const validResults: UnifiedSearchResult[] = [];
      for (const result of searchResults.results) {
        const task = parsedTasks.find((t) => t.id === result.metadata.taskId);
        if (!task) continue;

        const solution = parsedSolutions.find(
          (s) => s.id === task.prompts.promptCraftV4
        );

        validResults.push({
          task: {
            id: task.id,
            taskName: task.taskName,
            taskDescription: task.taskDescription,
            roles: task.roles,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt,
            mostRecentSyncToVectorDatabase: task.mostRecentSyncToVectorDatabase,
          },
          solution: solution
            ? {
                id: solution.id,
                name: solution.name,
                description: solution.description,
                prompt: solution.prompt,
                inputs: solution.inputs,
                outputs: solution.outputs,
                example_result: solution.example_result,
                createdAt: solution.createdAt,
                updatedAt: solution.updatedAt,
              }
            : undefined,
          searchMetadata: {
            score: result.score,
            searchType: "pinecone" as const,
          },
        });
      }
      results = validResults;

      nextCursor = searchResults.nextCursor;
      hasNextPage = searchResults.hasNextPage;
      totalCount = searchResults.totalCount;
    }
  } else {
    // Use MongoDB for browsing (no query)
    const dbResults = await getTasksLimited({
      limit: limit + 1, // Get one extra to check if there are more
      cursor,
      roleFilter,
    });

    await connectMongo();

    // Get solutions for these tasks
    const solutionIds = dbResults.tasks
      .map((task) => task.prompts.promptCraftV4)
      .filter(Boolean);

    const solutions = await Solution.find({
      _id: { $in: solutionIds },
    });
    const parsedSolutions = solutions.map((solution) =>
      solutionMongooseParser.parse(solution)
    );

    // Check if there are more results
    hasNextPage = dbResults.tasks.length > limit;
    const tasks = hasNextPage
      ? dbResults.tasks.slice(0, limit)
      : dbResults.tasks;
    nextCursor = hasNextPage ? tasks[tasks.length - 1]?.id : undefined;

    results = tasks.map((task) => {
      const solution = parsedSolutions.find(
        (s) => s.id === task.prompts.promptCraftV4
      );

      return {
        task: {
          id: task.id,
          taskName: task.taskName,
          taskDescription: task.taskDescription,
          roles: task.roles,
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
          mostRecentSyncToVectorDatabase: task.mostRecentSyncToVectorDatabase,
        },
        solution: solution
          ? {
              id: solution.id,
              name: solution.name,
              description: solution.description,
              prompt: solution.prompt,
              inputs: solution.inputs,
              outputs: solution.outputs,
              example_result: solution.example_result,
              createdAt: solution.createdAt,
              updatedAt: solution.updatedAt,
            }
          : undefined,
        searchMetadata: {
          searchType: "mongodb" as const,
        },
      };
    });

    // Get total count for browse mode
    totalCount = await getTotalTaskCount({ roleFilter });
  }

  return {
    results,
    nextCursor,
    hasNextPage,
    totalCount,
  };
}

export async function executeUnifiedSearch(
  input: UnifiedSearchInput
): Promise<UnifiedSearchResponse> {
  const hasQuery = input.query.trim().length > 0;

  // Only cache for browse mode (empty query) and first page (no cursor)
  if (!hasQuery && !input.cursor) {
    const cacheKey = createSearchCacheKey(input);
    return await cache.getOrSet(
      cacheKey,
      () => executeUnifiedSearchInternal(input),
      {
        ttl: 300, // 5 minutes
        prefix: "search",
      }
    );
  }

  // For search queries or pagination, don't cache
  return await executeUnifiedSearchInternal(input);
}
