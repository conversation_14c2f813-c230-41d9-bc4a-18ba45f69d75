const saasCopy = {
  hero: {
    title: "Automate your work processes, not your thinking",
    subtitle:
      "The AI-powered platform that connects knowledge workers with curated solutions to automate time-consuming and repetitive tasks at work.",
  },
  featuresSection: {
    title: {
      nonHighlighted: "Everything you need to identify and automate",
      highlighted: "workplace processes",
    },
  },
  pricing: {
    subtitle:
      "Stop wasting time on repetitive tasks and focus on what matters!",
  },
  callToAction: {
    title: "Identify, automate, optimize",
    subtitle: "Don't let manual processes slow down your productivity...",
  },
  faq: {
    whatDoIGetExactly: `1/ Access to our curated directory of AI solutions organized by role and task, helping you quickly find relevant automation tools for your specific work processes.
  
  2/ With the Pro plan, unlock our AI solution generation engine that creates custom automation solutions tailored to your unique processes and workflows.
  
  3/ Enterprise customers get access to our process identification system that integrates with your knowledge base to automatically discover automation opportunities across your organization, plus a private directory with SSO integration.
  `,
  },
} as const;

/**
   * Prompt: 
   * 
  Re-create this code snippet and keep the same exact structure.
  Replace all of the copy with copy for the following app: <APP DESCRIPTION>
  
  const saasCopy = {
    hero: {
      title: "Ship your startup in days,not weeks",
      subtitle: "The NextJS boilerplate with all you need to build your SaaS, AI tool, or any other web app and make your first $ online fast.",
    },
    featuresSection: {
      title: {
        nonHighlighted: "All you need to ship your startup fast",
        highlighted: "and get profitable",
      },
    },
    pricing: {
      subtitle: "Save hours of repetitive code and ship faster!",
    },
    callToAction: {
      title: "Boost your app, launch, earn",
      subtitle: "Get inspired and avoid burnout",
    },
    faq: {
      whatDoIGetExactly:
        `1/ The NextJS starter with all the boilerplate code you need to run an online business: a payment system, a database, login, a blog, UI components, and much more.
  The repo is available in:
  - Javascript and Typescript
  - /app router and /pages router.
  
  2/ The documentation helps you set up your app from scratch, write copy that sells, and ship fast.
  
  3/ With the Premium plan, access to our Discord with 5,000+ makers, the Leaderboards to showcase your startup, and $1,210 worth of unique discounts.
  `,
    },
  } as const;
  
   */

export default saasCopy;
