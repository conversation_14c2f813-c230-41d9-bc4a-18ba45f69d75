import { NextRequest } from "next/server";
import { DISCORD_WEBHOOK } from "@/libs/discord";
import { randomUUID } from "crypto";

export async function sendErrorToDiscord(
  error: unknown,
  req: NextRequest,
  requestData?: unknown
) {
  try {
    const requestId = req.headers.get("x-request-id") || randomUUID();
    const errorMessage =
      error instanceof Error ? error.message : JSON.stringify(error);
    const truncatedError =
      errorMessage.length > 4000
        ? errorMessage.slice(0, 4000) + "..."
        : errorMessage;

    // First message: Embed with metadata
    await DISCORD_WEBHOOK.send({
      embeds: [
        {
          title: "🚨 API Error",
          fields: [
            {
              name: "Request ID",
              value: requestId,
              inline: true,
            },
            {
              name: "Endpoint",
              value: req.url || "Unknown",
              inline: true,
            },
            {
              name: "Method",
              value: req.method || "Unknown",
              inline: true,
            },
            {
              name: "IP Address",
              value:
                req instanceof NextRequest ? req.ip || "Unknown" : "Unknown",
              inline: true,
            },
            {
              name: "User Agent",
              value: (req.headers.get("user-agent") || "Unknown").slice(
                0,
                1024
              ),
              inline: false,
            },
          ],
          timestamp: new Date().toISOString(),
          color: 0xff0000, // Red color
        },
      ],
    });

    // Second message: Request data
    await DISCORD_WEBHOOK.send(
      `**Request ID: ${requestId}**\nRequest Data:\n\`\`\`json\n${JSON.stringify(
        requestData || "No request data",
        null,
        2
      )}\`\`\``
    );

    // Third message: Error details
    await DISCORD_WEBHOOK.send(
      `**Request ID: ${requestId}**\nError:\n\`\`\`\n${truncatedError}\`\`\``
    );
  } catch (webhookError) {
    console.error("Failed to send error to Discord:", webhookError);
  }
}
