import {
  Bookmark,
  Folder,
  FileText,
  Star,
  Heart,
  Tag,
  Library,
  Archive,
  Briefcase,
  GraduationCap,
  Book,
  Clipboard,
  Layers,
  Grid3x3,
  List,
  Target,
  Trophy,
  Lightbulb,
  Palette,
  Music,
  Image,
  Video,
  Code,
  Database,
  Settings,
  Puzzle,
  Gamepad,
  Calendar,
  Clock,
  MapPin,
} from "lucide-react";

// Collection icon mapping
export const COLLECTION_ICON_MAP = {
  bookmark: Bookmark,
  folder: Folder,
  "file-text": FileText,
  star: Star,
  heart: Heart,
  tag: Tag,
  library: Library,
  archive: Archive,
  briefcase: Briefcase,
  "graduation-cap": GraduationCap,
  book: Book,
  clipboard: Clipboard,
  layers: Layers,
  grid: Grid3x3,
  list: List,
  target: Target,
  trophy: Trophy,
  lightbulb: Lightbulb,
  palette: Palette,
  music: Music,
  image: Image,
  video: Video,
  code: Code,
  database: Database,
  settings: Settings,
  puzzle: Puzzle,
  gamepad: Gamepad,
  calendar: Calendar,
  clock: Clock,
  "map-pin": MapPin,
} as const;

export function getCollectionIcon(iconName: string) {
  return (
    COLLECTION_ICON_MAP[iconName as keyof typeof COLLECTION_ICON_MAP] ||
    Bookmark
  );
}

// Collection color mapping
export const COLLECTION_COLOR_MAP = {
  blue: "text-blue-600 bg-blue-50 border-blue-200",
  purple: "text-purple-600 bg-purple-50 border-purple-200",
  green: "text-green-600 bg-green-50 border-green-200",
  red: "text-red-600 bg-red-50 border-red-200",
  orange: "text-orange-600 bg-orange-50 border-orange-200",
  yellow: "text-yellow-600 bg-yellow-50 border-yellow-200",
  pink: "text-pink-600 bg-pink-50 border-pink-200",
  indigo: "text-indigo-600 bg-indigo-50 border-indigo-200",
  teal: "text-teal-600 bg-teal-50 border-teal-200",
  slate: "text-slate-600 bg-slate-50 border-slate-200",
} as const;

export function getCollectionColorClasses(color: string) {
  return (
    COLLECTION_COLOR_MAP[color as keyof typeof COLLECTION_COLOR_MAP] ||
    COLLECTION_COLOR_MAP.blue
  );
}

// Color button backgrounds for the color picker
export const COLLECTION_COLOR_BUTTON_MAP = {
  blue: "bg-blue-500",
  purple: "bg-purple-500",
  green: "bg-green-500",
  red: "bg-red-500",
  orange: "bg-orange-500",
  yellow: "bg-yellow-500",
  pink: "bg-pink-500",
  indigo: "bg-indigo-500",
  teal: "bg-teal-500",
  slate: "bg-slate-500",
} as const;

export function getCollectionColorButtonClass(color: string) {
  return (
    COLLECTION_COLOR_BUTTON_MAP[
      color as keyof typeof COLLECTION_COLOR_BUTTON_MAP
    ] || COLLECTION_COLOR_BUTTON_MAP.blue
  );
}
