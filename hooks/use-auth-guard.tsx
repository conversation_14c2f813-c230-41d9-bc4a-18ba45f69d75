"use client";

import { useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { SignInDialog } from "@/components/auth/signin-dialog";

interface UseAuthGuardProps {
  title?: string;
  description?: string;
  feature?: string;
}

export function useAuthGuard({
  title = "Sign in to continue",
  description = "Create a free account to access this feature",
  feature = "this feature",
}: UseAuthGuardProps = {}) {
  const { data: session, status } = useSession();
  const [showSignInDialog, setShowSignInDialog] = useState(false);

  const executeWithAuth = useCallback(
    (callback: () => void) => {
      if (status === "loading") {
        return; // Don't execute while loading
      }

      if (session) {
        // User is authenticated, execute the callback
        callback();
      } else {
        // User is not authenticated, show sign-in dialog
        setShowSignInDialog(true);
      }
    },
    [session, status]
  );

  const handleSignInSuccess = useCallback(() => {
    setShowSignInDialog(false);
    // The callback will be executed automatically when the user is authenticated
    // due to the session dependency in executeWithAuth
  }, []);

  const SignInDialogComponent = useCallback(
    () => (
      <SignInDialog
        open={showSignInDialog}
        onOpenChange={setShowSignInDialog}
        onSuccess={handleSignInSuccess}
        title={title}
        description={description}
      />
    ),
    [showSignInDialog, handleSignInSuccess, title, description]
  );

  return {
    isAuthenticated: !!session,
    isLoading: status === "loading",
    executeWithAuth,
    SignInDialog: SignInDialogComponent,
  };
}
