import { useEffect, useRef } from "react";
import { trpc } from "@/utils/trpc/client";

/**
 * Hook to track solution page views with deduplication
 */
export function useTrackSolutionView(solutionId: string) {
  const hasTracked = useRef(false);
  const mutation = trpc.analytics.trackSolutionView.useMutation();

  useEffect(() => {
    // Only track once per component mount
    if (hasTracked.current || !solutionId) return;

    // Create a simple client-side fingerprint
    const fingerprint = [
      navigator.language,
      screen.width,
      screen.height,
      new Date().getTimezoneOffset(),
    ].join("|");

    // Track view after a 2-second delay to ensure it's a meaningful view
    const timeoutId = setTimeout(() => {
      if (!hasTracked.current) {
        hasTracked.current = true;

        mutation.mutate({
          solutionId,
          userAgent: navigator.userAgent,
          fingerprint,
        });
      }
    }, 2000);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [solutionId, mutation]);

  return {
    isTracking: mutation.isLoading,
    error: mutation.error,
    data: mutation.data,
  };
}
