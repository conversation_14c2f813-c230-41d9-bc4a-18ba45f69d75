import { trpc } from "@/utils/trpc/client";

/**
 * Hook to track solution usage when users interact with run/copy buttons
 */
export function useTrackSolutionUsage() {
  const mutation = trpc.analytics.trackSolutionUsage.useMutation();

  const trackUsage = (solutionId: string) => {
    if (!solutionId) return;

    // Create a simple client-side fingerprint
    const fingerprint = [
      navigator.language,
      screen.width,
      screen.height,
      new Date().getTimezoneOffset(),
    ].join("|");

    mutation.mutate({
      solutionId,
      userAgent: navigator.userAgent,
      fingerprint,
    });
  };

  return {
    trackUsage,
    isTracking: mutation.isLoading,
    error: mutation.error,
    data: mutation.data,
  };
}
