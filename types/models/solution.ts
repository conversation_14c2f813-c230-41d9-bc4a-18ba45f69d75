import { z } from "zod";

const solutionBaseSchema = {
  name: z.string().min(1),
  description: z.string().min(1),
  prompt: z.string().min(1),
  inputs: z.array(z.string()),
  outputs: z.array(z.string()),
  example_result: z.optional(
    z.object({
      conversation: z.array(
        z.object({
          role: z.string(),
          message: z.string(),
        })
      ),
      generationModel: z.string(),
    })
  ),
};

export const solutionCreateSchema = z.object({
  ...solutionBaseSchema,
});

export const solutionUpdateSchema = z.object({
  id: z.string(),
  ...solutionBaseSchema,
});

export const solutionMongooseParser = z
  .object({
    _id: z.any(),
    name: z.string(),
    description: z.string(),
    prompt: z.string(),
    inputs: z.array(z.string()),
    outputs: z.array(z.string()),
    example_result: z.optional(
      z.object({
        conversation: z.array(
          z.object({
            role: z.string(),
            message: z.string(),
          })
        ),
        generationModel: z.string(),
      })
    ),
    createdAt: z.date(),
    updatedAt: z.date(),
    // viewCount exists in DB but is explicitly excluded from frontend
  })
  .transform((data) => ({
    id: data._id.toString(),
    name: data.name,
    description: data.description,
    prompt: data.prompt,
    inputs: data.inputs,
    outputs: data.outputs,
    example_result: data.example_result,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  }));

export type SolutionItem = z.infer<typeof solutionMongooseParser>;
