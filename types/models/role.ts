import { z } from "zod";

const roleBaseSchema = {
  roleName: z.string().min(1),
  roleDescription: z.string().min(1),
};

export const roleCreateSchema = z.object({
  ...roleBaseSchema,
});

export const roleUpdateSchema = z.object({
  id: z.string(),
  ...roleBaseSchema,
});

export const roleMongooseParser = z
  .object({
    _id: z.any(),
    roleName: z.string(),
    roleDescription: z.string(),
    createdAt: z.date(),
    updatedAt: z.date(),
  })
  .transform((data) => ({
    id: data._id.toString(),
    roleName: data.roleName,
    roleDescription: data.roleDescription,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  }));

export type RoleItem = z.infer<typeof roleMongooseParser>;
