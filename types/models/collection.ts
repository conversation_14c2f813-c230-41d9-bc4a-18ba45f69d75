import { z } from "zod";
import { TCollection } from "@/models/Collection";

// Zod schemas for API validation
export const createCollectionSchema = z.object({
  name: z.string().min(1, "Collection name is required").max(100),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional().default(false),
  icon: z.string().min(1, "Icon is required"),
  color: z.string().min(1, "Color is required"),
  taskId: z.string().optional(),
});

export const updateCollectionSchema = z.object({
  name: z.string().min(1, "Collection name is required").max(100).optional(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional(),
  icon: z.string().min(1, "Icon is required").optional(),
  color: z.string().min(1, "Color is required").optional(),
});

export const addTaskToCollectionSchema = z.object({
  taskId: z.string(),
});

export const removeTaskFromCollectionSchema = z.object({
  taskId: z.string(),
});

// Zod parser for MongoDB collection with populated tasks
export const collectionDetailMongooseParser = z
  .any()
  .transform((collection) => ({
    id: collection._id.toString(),
    name: collection.name,
    description: collection.description,
    userId: collection.userId.toString(),
    tasks: collection.tasks.map((task: any) => ({
      id: task._id.toString(),
      taskName: task.taskName,
      taskDescription: task.taskDescription,
      roles: task.roles.map((role: any) => ({
        id: role._id.toString(),
        roleName: role.roleName,
        roleSlug: role.roleSlug,
      })),
      prompts: {
        promptCraftV4: task.prompts?.promptCraftV4?.toString(),
      },
      updatedAt: task.updatedAt.toISOString(),
      createdAt: task.createdAt.toISOString(),
    })),
    updatedAt: collection.updatedAt.toISOString(),
    createdAt: collection.createdAt.toISOString(),
    isPublic: collection.isPublic,
    icon: collection.icon,
    color: collection.color,
  }));

// Frontend-safe type (removes sensitive fields)
export type CollectionData = Omit<TCollection, "userId"> & {
  id: string;
  userId: string;
  tasks: string[];
  icon: string;
  color: string;
};

// Parser function to convert Mongoose document to frontend-safe type
export function collectionMongooseParser(collection: any): CollectionData {
  return {
    id: collection._id.toString(),
    userId: collection.userId.toString(),
    name: collection.name,
    description: collection.description,
    tasks: collection.tasks.map((task: any) => task.toString()),
    updatedAt: collection.updatedAt,
    createdAt: collection.createdAt,
    isPublic: collection.isPublic,
    icon: collection.icon,
    color: collection.color,
  };
}

export type CreateCollectionRequest = z.infer<typeof createCollectionSchema>;
export type UpdateCollectionRequest = z.infer<typeof updateCollectionSchema>;
export type AddTaskToCollectionRequest = z.infer<
  typeof addTaskToCollectionSchema
>;
export type RemoveTaskFromCollectionRequest = z.infer<
  typeof removeTaskFromCollectionSchema
>;

// Type for collection detail (inferred from parser)
export type CollectionDetail = z.infer<typeof collectionDetailMongooseParser>;

// Frontend collection item type for components
export type CollectionItem = {
  id: string;
  name: string;
  description?: string;
  tasks: string[];
  icon: string;
  color: string;
};
