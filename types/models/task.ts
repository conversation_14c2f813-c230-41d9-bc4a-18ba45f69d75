import { z } from "zod";

const taskBaseSchema = {
  taskName: z.string().min(1),
  taskDescription: z.string().min(1),
  prompts: z.object({
    promptCraftV4: z.string().optional(),
  }),
  roles: z.array(z.string()),
};

export const taskCreateSchema = z.object({
  ...taskBaseSchema,
});

export const taskUpdateSchema = z.object({
  id: z.string(),
  ...taskBaseSchema,
});

export const taskMongooseParser = z
  .object({
    _id: z.any(),
    taskName: z.string(),
    taskDescription: z.string(),
    prompts: z.object({
      promptCraftV4: z.any().optional(),
    }),
    roles: z.array(z.any()),
    mostRecentSyncToVectorDatabase: z.date().optional(),
    createdAt: z.date(),
    updatedAt: z.date(),
  })
  .transform((data) => ({
    id: data._id.toString(),
    taskName: data.taskName,
    taskDescription: data.taskDescription,
    prompts: {
      promptCraftV4: data.prompts.promptCraftV4?.toString(),
    },
    roles: data.roles.map((role: any) =>
      typeof role === "string" ? role : role._id.toString()
    ),
    mostRecentSyncToVectorDatabase: data.mostRecentSyncToVectorDatabase,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  }));

export type TaskItem = z.infer<typeof taskMongooseParser>;
