import { z } from "zod";

const onboardingSchema = z.object({
  completed: z.boolean(),
  step: z.enum(["welcome", "finished"]),
});

export const updateOnboardingStepSchema = onboardingSchema;

const userBaseSchema = {
  name: z.string().optional(),
  email: z.string().email(),
  image: z.string().optional(),
  customerId: z.string().optional(),
  priceId: z.string().optional(),
  hasAccess: z.boolean(),
  onboarding: onboardingSchema,
};

export const userCreateSchema = z.object({
  ...userBaseSchema,
});

export const userUpdateSchema = z.object({
  id: z.string(),
  ...userBaseSchema,
});

export const userMongooseParser = z
  .object({
    _id: z.any(),
    name: z.string().optional(),
    email: z.string(),
    image: z.string().optional(),
    customerId: z.string().optional(),
    priceId: z.string().optional(),
    hasAccess: z.boolean(),
    onboarding: onboardingSchema,
  })
  .transform((data) => ({
    id: data._id.toString(),
    name: data.name,
    email: data.email,
    image: data.image,
    customerId: data.customerId,
    priceId: data.priceId,
    hasAccess: data.hasAccess,
    onboarding: data.onboarding,
  }));

export type UserItem = z.infer<typeof userMongooseParser>;
export type OnboardingStepId = z.infer<typeof onboardingSchema>["step"];
