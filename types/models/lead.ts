import { z } from "zod";

const leadBaseSchema = {
  email: z.string().email(),
};

export const leadCreateSchema = z.object({
  ...leadBaseSchema,
});

export const leadUpdateSchema = z.object({
  id: z.string(),
  ...leadBaseSchema,
});

export const leadMongooseParser = z
  .object({
    _id: z.any(),
    email: z.string(),
    createdAt: z.date(),
    updatedAt: z.date(),
  })
  .transform((data) => ({
    id: data._id.toString(),
    email: data.email,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  }));

export type LeadItem = z.infer<typeof leadMongooseParser>;
