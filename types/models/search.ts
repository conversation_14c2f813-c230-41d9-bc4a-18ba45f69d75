import { z } from "zod";
import { PineconeMetadataSchema } from "@/libs/pinecone";

/**
 * Input schema for unified task search (supports both search and browse modes)
 */
export const unifiedSearchSchema = z.object({
  query: z.string().optional().default(""), // Empty query means browse all tasks
  roleFilter: z.array(z.string()).optional(),
  limit: z.number().min(1).max(50).default(20),
  cursor: z.string().optional(),
  minScore: z.number().min(0).max(1).default(0.3),
});

/**
 * Legacy search schema for backward compatibility
 */
export const searchTasksSchema = z.object({
  query: z
    .string()
    .min(1, "Search query cannot be empty")
    .max(500, "Search query too long"),
  limit: z.number().min(1).max(50).default(20),
  cursor: z.string().optional(),
  minScore: z.number().min(0).max(1).default(0.6),
  roleFilter: z.array(z.string()).optional(),
});

/**
 * Search result item with score and metadata (legacy Pinecone-only)
 */
export const searchResultSchema = z.object({
  id: z.string(),
  score: z.number(),
  metadata: PineconeMetadataSchema,
});

/**
 * Unified search response item schema
 */
export const unifiedSearchResultSchema = z.object({
  task: z.object({
    id: z.string(),
    taskName: z.string(),
    taskDescription: z.string(),
    roles: z.array(z.string()),
    createdAt: z.date(),
    updatedAt: z.date(),
    mostRecentSyncToVectorDatabase: z.date().optional(),
  }),
  solution: z
    .object({
      id: z.string(),
      name: z.string(),
      description: z.string(),
      prompt: z.string(),
      inputs: z.array(z.string()),
      outputs: z.array(z.string()),
      example_result: z
        .object({
          conversation: z.array(
            z.object({
              role: z.string(),
              message: z.string(),
            })
          ),
          generationModel: z.string(),
        })
        .optional(),
      createdAt: z.date(),
      updatedAt: z.date(),
    })
    .optional(),
  searchMetadata: z.object({
    score: z.number().optional(),
    searchType: z.enum(["pinecone", "mongodb"]),
  }),
});

/**
 * Legacy search response schema
 */
export const searchResponseSchema = z.object({
  results: z.array(searchResultSchema),
  nextCursor: z.string().optional(),
  hasNextPage: z.boolean(),
  query: z.string(),
  processingTimeMs: z.number(),
  totalCount: z.number(),
});

/**
 * Unified search response schema
 */
export const unifiedSearchResponseSchema = z.object({
  results: z.array(unifiedSearchResultSchema),
  nextCursor: z.string().optional(),
  hasNextPage: z.boolean(),
  totalCount: z.number(),
});

// Type exports
export type UnifiedSearchInput = z.infer<typeof unifiedSearchSchema>;
export type UnifiedSearchResult = z.infer<typeof unifiedSearchResultSchema>;
export type UnifiedSearchResponse = z.infer<typeof unifiedSearchResponseSchema>;

// Legacy type exports
export type SearchTasksInput = z.infer<typeof searchTasksSchema>;
export type SearchResult = z.infer<typeof searchResultSchema>;
export type SearchResponse = z.infer<typeof searchResponseSchema>;
