import mongoose, { Document, Model } from "mongoose";

export type TTask = {
  taskName: string;
  taskDescription: string;
  prompts: {
    promptCraftV4?: mongoose.Types.ObjectId;
  };
  roles: mongoose.Types.ObjectId[];
  mostRecentSyncToVectorDatabase?: Date;
  createdAt: Date;
  updatedAt: Date;
};

export interface ITask extends TTask, Document {}

const taskSchema = new mongoose.Schema<ITask>(
  {
    taskName: {
      type: String,
      trim: true,
      required: true,
    },
    taskDescription: {
      type: String,
      trim: true,
      required: true,
    },
    prompts: {
      promptCraftV4: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Solution",
      },
    },
    roles: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Role",
        required: true,
      },
    ],
    mostRecentSyncToVectorDatabase: {
      type: Date,
      required: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const Task: Model<ITask> =
  mongoose.models.Task || mongoose.model<ITask>("Task", taskSchema);
export default Task;
