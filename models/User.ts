import mongoose, { Document, Model } from "mongoose";
import { OnboardingStepId } from "@/types/models/user";

// Define the User interface
export type TUser = {
  name: string;
  email: string;
  image?: string;
  customerId?: string;
  priceId?: string;
  hasAccess: boolean;
  onboarding: {
    completed: boolean;
    step: OnboardingStepId;
  };
  createdAt: Date;
  updatedAt: Date;
};

export interface IUser extends TUser, Document {}

// USER SCHEMA
const userSchema = new mongoose.Schema<IUser>(
  {
    name: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      private: true,
    },
    image: {
      type: String,
    },
    customerId: {
      type: String,
      validate(value: string) {
        return value.includes("cus_");
      },
    },
    priceId: {
      type: String,
      validate(value: string) {
        return value.includes("price_");
      },
    },
    hasAccess: {
      type: Boolean,
      default: false,
    },
    onboarding: {
      completed: {
        type: Boolean,
        default: true, // TODO: after implementing onboarding workflow, change to false
      },
      step: {
        type: String,
        enum: ["welcome", "finished"] satisfies OnboardingStepId[],
        default: "welcome",
      },
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Define and export the User model
const User: Model<IUser> =
  mongoose.models.User || mongoose.model<IUser>("User", userSchema);
export default User;
