import mongoose, { Document, Model } from "mongoose";

export type TLead = {
  email: string;
  createdAt: Date;
  updatedAt: Date;
};

export interface ILead extends TLead, Document {}

// LEAD SCHEMA is used to store the leads that are generated from the landing page.
// You would use this if your product isn't ready yet and you want to collect emails
// The <ButtonLead /> component & the /api/lead route are used to collect the emails
const leadSchema = new mongoose.Schema<ILead>(
  {
    email: {
      type: String,
      trim: true,
      lowercase: true,
      private: true,
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const Lead: Model<ILead> =
  mongoose.models.Lead || mongoose.model<ILead>("Lead", leadSchema);
export default Lead;
