import mongoose, { Document, Model } from "mongoose";

export type TCollection = {
  name: string;
  description?: string;
  userId: mongoose.Types.ObjectId;
  tasks: mongoose.Types.ObjectId[];
  isPublic: boolean;
  icon: string;
  color: string;
  createdAt: Date;
  updatedAt: Date;
};

export interface ICollection extends TCollection, Document {}

const collectionSchema = new mongoose.Schema<ICollection>(
  {
    name: {
      type: String,
      trim: true,
      required: true,
      maxlength: 100,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    tasks: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Task",
      },
    ],
    isPublic: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: "bookmark",
      required: true,
    },
    color: {
      type: String,
      default: "blue",
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Add indexes for better query performance
collectionSchema.index({ userId: 1 });
collectionSchema.index({ userId: 1, name: 1 }, { unique: true });

const Collection: Model<ICollection> =
  mongoose.models.Collection ||
  mongoose.model<ICollection>("Collection", collectionSchema);
export default Collection;
