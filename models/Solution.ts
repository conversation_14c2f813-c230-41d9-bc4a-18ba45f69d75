import mongoose, { Document, Model } from "mongoose";

export type TSolution = {
  name: string;
  description: string;
  prompt: string;
  inputs: string[];
  outputs: string[];
  viewCount: number;
  usageCount: number;
  example_result?: {
    conversation: {
      role: string;
      message: string;
    }[];
    generationModel: string;
  };
  createdAt: Date;
  updatedAt: Date;
};

export interface ISolution extends TSolution, Document {}

const solutionSchema = new mongoose.Schema<ISolution>(
  {
    name: {
      type: String,
      trim: true,
      required: true,
    },
    description: {
      type: String,
      trim: true,
      required: true,
    },
    prompt: {
      type: String,
      trim: true,
      required: true,
    },
    inputs: [
      {
        type: String,
        trim: true,
        required: true,
      },
    ],
    outputs: [
      {
        type: String,
        trim: true,
        required: true,
      },
    ],
    viewCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    usageCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    example_result: {
      type: {
        conversation: [
          {
            role: {
              type: String,
              required: true,
            },
            message: {
              type: String,
              required: true,
            },
          },
        ],
        generationModel: {
          type: String,
          required: true,
        },
      },
      required: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const Solution: Model<ISolution> =
  mongoose.models.Solution ||
  mongoose.model<ISolution>("Solution", solutionSchema);
export default Solution;
