import mongoose, { Document, Model } from "mongoose";

export type TRole = {
  roleName: string;
  roleDescription: string;
  createdAt: Date;
  updatedAt: Date;
};

export interface IRole extends TRole, Document {}

const roleSchema = new mongoose.Schema<IRole>(
  {
    roleName: {
      type: String,
      trim: true,
      required: true,
    },
    roleDescription: {
      type: String,
      trim: true,
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

const Role: Model<IRole> =
  mongoose.models.Role || mongoose.model<IRole>("Role", roleSchema);
export default Role;
