# -----------------------------------------------------------------------------
# NextAuth: https://shipfa.st/docs/tutorials/user-auth
# -----------------------------------------------------------------------------
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=

# -----------------------------------------------------------------------------
# Google OAuth: https://shipfa.st/docs/features/google-oauth
# -----------------------------------------------------------------------------
GOOGLE_ID=
GOOGLE_SECRET=

# -----------------------------------------------------------------------------
# Resend: https://shipfa.st/docs/features/emails
# -----------------------------------------------------------------------------
RESEND_API_KEY=

# -----------------------------------------------------------------------------
# Database URI: https://shipfa.st/docs/features/database
# -----------------------------------------------------------------------------
MONGODB_URI=

# -----------------------------------------------------------------------------
# Stripe: https://shipfa.st/docs/features/payments
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# -----------------------------------------------------------------------------
# Upstash: https://shipfa.st/docs/security/rate-limiting-api-routes
# -----------------------------------------------------------------------------
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=

# -----------------------------------------------------------------------------
# Discord
# -----------------------------------------------------------------------------
DISCORD_WEBHOOK_URL=
DISCORD_SAMPLES_WEBHOOK_URL=

# -----------------------------------------------------------------------------
# Feature flags: libs/env.ts
# -----------------------------------------------------------------------------
BLOG_ENABLED_FLAG=false
WAITLIST_MODE_FLAG=true

# -----------------------------------------------------------------------------
# Stage: libs/env.ts
# -----------------------------------------------------------------------------
STAGE=development

# -----------------------------------------------------------------------------
# OpenAI
# -----------------------------------------------------------------------------
OPENAI_API_KEY=

# -----------------------------------------------------------------------------
# Pinecone
# -----------------------------------------------------------------------------
PINECONE_API_KEY=
PINECONE_INDEX_NAME=