"use client";

import { useState, useMemo, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { trpc } from "@/utils/trpc/client";
import { Loader2, Plus, BookmarkPlus, Check, X } from "lucide-react";
import { toast } from "react-hot-toast";
import {
  getCollectionIcon,
  getCollectionColorClasses,
} from "@/lib/collection-utils";
import { CollectionDialog } from "./collection-dialog";
import { CollectionItem } from "@/types/models/collection";
import { useAuthGuard } from "@/hooks/use-auth-guard";
import { useRouter } from "next/navigation";
import { COLLECTION_LIMIT } from "@/lib/constants";

interface AddToCollectionDialogProps {
  taskId: string | null;
  solutionName: string;
  collections: CollectionItem[] | null;
  trigger?: React.ReactNode;
}

export function AddToCollectionDialog({
  taskId,
  solutionName,
  collections,
  trigger,
}: AddToCollectionDialogProps) {
  const [open, setOpen] = useState(false);
  const [createCollectionOpen, setCreateCollectionOpen] = useState(false);
  const [loadingCollectionId, setLoadingCollectionId] = useState<string | null>(
    null
  );
  const [hasChanges, setHasChanges] = useState(false);

  const router = useRouter();

  const { isAuthenticated, executeWithAuth, SignInDialog } = useAuthGuard({
    title: "Sign in to manage collections",
    description: "Create a free account to save solutions to your collections",
    feature: "collection management",
  });

  // Get tRPC utils for invalidating queries
  const utils = trpc.useUtils();

  // Use tRPC query to get live collections data
  const { data: liveCollections } =
    trpc.collections.getUserCollections.useQuery(
      { limit: COLLECTION_LIMIT }, // Reasonable limit for collections
      {
        enabled: isAuthenticated && open, // Only run when authenticated and dialog is open
      }
    );

  // Check if data has changed when dialog closes
  useEffect(() => {
    if (!open && hasChanges) {
      router.refresh();
      setHasChanges(false);
    }
  }, [open, hasChanges, router]);

  // Use live collections data if available, otherwise fall back to props
  const currentCollections = useMemo(() => {
    return (
      liveCollections?.map((collection) => ({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        tasks: collection.tasks,
        icon: collection.icon,
        color: collection.color,
      })) ||
      collections ||
      []
    );
  }, [liveCollections, collections]);

  // Separate collections into selected (contains task) and unselected
  const { selectedCollections, unselectedCollections } = useMemo(() => {
    if (!currentCollections || !taskId) {
      return { selectedCollections: [], unselectedCollections: [] };
    }

    const selected = currentCollections.filter((collection) =>
      collection.tasks.includes(taskId)
    );
    const unselected = currentCollections.filter(
      (collection) => !collection.tasks.includes(taskId)
    );

    return {
      selectedCollections: selected,
      unselectedCollections: unselected,
    };
  }, [currentCollections, taskId]);

  // Add task to collection mutation
  const addToCollectionMutation =
    trpc.collections.addTaskToCollection.useMutation({
      onSuccess: ({ message }) => {
        toast.success(message);
        // Invalidate the collections query to refresh the data
        utils.collections.getUserCollections.invalidate();
        setLoadingCollectionId(null);
        setHasChanges(true);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to add task to collection");
        setLoadingCollectionId(null);
      },
    });

  // Remove task from collection mutation
  const removeFromCollectionMutation =
    trpc.collections.removeTaskFromCollection.useMutation({
      onSuccess: ({ message }) => {
        toast.success(message);
        // Invalidate the collections query to refresh the data
        utils.collections.getUserCollections.invalidate();
        setLoadingCollectionId(null);
        setHasChanges(true);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to remove task from collection");
        setLoadingCollectionId(null);
      },
    });

  const handleToggleCollection = async (
    collectionId: string,
    isCurrentlySelected: boolean
  ) => {
    if (!taskId) {
      toast.error("No task found for this solution");
      return;
    }

    setLoadingCollectionId(collectionId);

    try {
      if (isCurrentlySelected) {
        await removeFromCollectionMutation.mutateAsync({
          taskId,
          collectionId,
        });
      } else {
        await addToCollectionMutation.mutateAsync({
          taskId,
          collectionId,
        });
      }
    } catch {
      // Error handling is done in the mutation onError callbacks
    }
  };

  const handleCreateCollectionSuccess = () => {
    setCreateCollectionOpen(false);
    setHasChanges(true);
    // The collections query will be invalidated by the CollectionDialog
  };

  if (!isAuthenticated) {
    return (
      <>
        <Button
          variant="outline"
          onClick={() => executeWithAuth(() => setOpen(true))}
          className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3"
        >
          <BookmarkPlus className="w-3 h-3 sm:w-4 sm:h-4" />
          Save
        </Button>
        <SignInDialog />
      </>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {trigger || (
            <Button
              variant="outline"
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3"
            >
              <BookmarkPlus className="w-3 h-3 sm:w-4 sm:h-4" />
              Save
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Manage Collections for &quot;{solutionName}&quot;
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {!taskId ? (
              <div className="text-center py-8">
                <p className="text-sm text-muted-foreground mb-4">
                  No task found for this solution. This solution cannot be added
                  to collections.
                </p>
              </div>
            ) : selectedCollections.length > 0 ||
              unselectedCollections.length > 0 ? (
              <div className="space-y-6">
                {/* Selected Collections */}
                {selectedCollections.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-green-600 dark:text-green-400 mb-3 flex items-center gap-2">
                      <Check className="w-4 h-4" />
                      Added to Collections ({
                        selectedCollections.length
                      })
                    </h4>
                    <div className="space-y-2 max-h-[200px] overflow-y-auto">
                      {selectedCollections.map((collection) => {
                        const IconComponent = getCollectionIcon(
                          collection.icon
                        );
                        const colorClasses = getCollectionColorClasses(
                          collection.color
                        );
                        const isLoading = loadingCollectionId === collection.id;

                        return (
                          <button
                            key={collection.id}
                            onClick={() =>
                              executeWithAuth(() =>
                                handleToggleCollection(collection.id, true)
                              )
                            }
                            disabled={isLoading}
                            className="w-full flex items-center gap-3 p-3 rounded-lg border-2 border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950 transition-colors hover:bg-green-100 dark:hover:bg-green-900 text-left"
                          >
                            <IconComponent
                              className={`w-5 h-5 flex-shrink-0 ${colorClasses.split(" ")[0]}`}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {collection.name}
                              </div>
                              {collection.description && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {collection.description}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground mt-1">
                                {collection.tasks.length} task
                                {collection.tasks.length !== 1 ? "s" : ""}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {isLoading ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <>
                                  <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                                  <X className="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                                </>
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Available Collections */}
                {unselectedCollections.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground mb-3">
                      Available Collections ({unselectedCollections.length})
                    </h4>
                    <div className="space-y-2 max-h-[200px] overflow-y-auto">
                      {unselectedCollections.map((collection) => {
                        const IconComponent = getCollectionIcon(
                          collection.icon
                        );
                        const colorClasses = getCollectionColorClasses(
                          collection.color
                        );
                        const isLoading = loadingCollectionId === collection.id;

                        return (
                          <button
                            key={collection.id}
                            onClick={() =>
                              executeWithAuth(() =>
                                handleToggleCollection(collection.id, false)
                              )
                            }
                            disabled={isLoading}
                            className="w-full flex items-center gap-3 p-3 rounded-lg border transition-colors hover:bg-accent text-left"
                          >
                            <IconComponent
                              className={`w-5 h-5 flex-shrink-0 ${colorClasses.split(" ")[0]}`}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {collection.name}
                              </div>
                              {collection.description && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {collection.description}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground mt-1">
                                {collection.tasks.length} task
                                {collection.tasks.length !== 1 ? "s" : ""}
                              </div>
                            </div>
                            {isLoading ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Plus className="w-4 h-4 text-muted-foreground" />
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookmarkPlus className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground mb-4">
                  You don&apos;t have any collections yet
                </p>
              </div>
            )}

            <div className="border-t pt-4 space-y-3">
              <Button
                variant="outline"
                onClick={() =>
                  executeWithAuth(() => setCreateCollectionOpen(true))
                }
                className="w-full flex items-center gap-2"
                disabled={!taskId}
              >
                <Plus className="w-4 h-4" />
                Create New Collection
              </Button>

              <Button
                onClick={() => setOpen(false)}
                className="w-full"
                variant="default"
              >
                Done
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <CollectionDialog
        open={createCollectionOpen}
        onOpenChange={setCreateCollectionOpen}
        onSuccess={handleCreateCollectionSuccess}
        taskId={taskId || undefined}
      />

      <SignInDialog />
    </>
  );
}
