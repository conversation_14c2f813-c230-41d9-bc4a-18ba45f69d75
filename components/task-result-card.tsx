import Link from "next/link";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ExampleResultsCard } from "@/components/example-results-card";
import { UnifiedSearchResult } from "@/types/models/search";
import { useState } from "react";

interface TaskResultCardProps {
  result: UnifiedSearchResult;
  getRoleName: (roleId: string) => string;
}

export function TaskResultCard({ result, getRoleName }: TaskResultCardProps) {
  const { task, solution, searchMetadata } = result;
  const [isExampleDialogOpen, setIsExampleDialogOpen] = useState(false);

  // If there's no solution, don't render anything
  if (!solution) {
    return null;
  }

  return (
    <>
      <Card className="hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/20 group">
        <Link href={`/solutions/${solution.id}`}>
          <CardHeader className="pb-4 cursor-pointer">
            <div className="flex items-start justify-between gap-4">
              <CardTitle className="text-lg sm:text-xl leading-tight group-hover:text-primary transition-colors">
                {task.taskName}
              </CardTitle>
            </div>
            <div className="flex items-center gap-2 mt-2 flex-wrap">
              {task.roles.map((roleId: string) => (
                <Badge key={roleId} variant="outline" className="text-xs">
                  {getRoleName(roleId)}
                </Badge>
              ))}
              <span className="text-xs text-muted-foreground">
                {task.updatedAt
                  ? new Date(task.updatedAt)
                      .toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        hour: "numeric",
                        minute: "2-digit",
                      })
                      .replace(",", " at") +
                    (new Date(task.updatedAt).getHours() >= 12 ? " PM" : " AM")
                  : "6 hours ago"}
              </span>
              {searchMetadata.score && (
                <Badge
                  variant="outline"
                  className="text-xs text-muted-foreground"
                >
                  {Math.round(searchMetadata.score * 100)}% match
                </Badge>
              )}
            </div>
          </CardHeader>
        </Link>
        <CardContent className="pt-0">
          <CardDescription className="text-sm leading-relaxed">
            {task.taskDescription}
          </CardDescription>
          <div className="mt-4 flex justify-end items-center gap-2">
            {solution.example_result && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExampleDialogOpen(true)}
                className="text-xs"
              >
                View example
              </Button>
            )}
            <Link href={`/solutions/${solution.id}`}>
              <Badge
                variant="default"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-1 cursor-pointer"
              >
                View Details
              </Badge>
            </Link>
          </div>
        </CardContent>
      </Card>

      {solution.example_result && (
        <Dialog
          open={isExampleDialogOpen}
          onOpenChange={setIsExampleDialogOpen}
        >
          <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[80vh] overflow-y-auto p-3 sm:p-6">
            <DialogHeader>
              <DialogTitle>{task.taskName} - Example</DialogTitle>
            </DialogHeader>
            <div className="min-w-0 flex-1">
              <ExampleResultsCard
                exampleResult={solution.example_result}
                className="border-none shadow-none"
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
