import Image from "next/image";
import config from "@/config";
import saasCopy from "@/lib/saas-copy";
import env from "@/libs/env";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

const buttonLocation = env.WAITLIST_MODE_FLAG ? "#hero" : "#pricing";

const CTA = () => {
  return (
    <section className="relative overflow-hidden min-h-screen flex items-center justify-center">
      <Image
        src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3540&q=80"
        alt="Community networking"
        className="object-cover object-center w-full"
        priority
        quality={90}
        fill
      />

      {/* Modern gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-background/50 via-muted/60 to-background/70"></div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="font-bold text-4xl md:text-6xl tracking-tight mb-6 md:mb-8 text-background-foreground leading-tight">
            {saasCopy.callToAction.title}
          </h2>

          <p className="text-lg font-semibold md:text-xl text-accent-foreground mb-10 md:mb-14 max-w-2xl mx-auto">
            {saasCopy.callToAction.subtitle}
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Link href={buttonLocation} className="w-full sm:w-auto">
              <button className="group relative w-full sm:w-auto overflow-hidden rounded-lg bg-primary px-8 py-4 text-primary-foreground transition-all duration-300 ease-out hover:bg-primary/90 shadow-md raise-on-hover">
                <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                  Get {config.metadata.appName}
                  <ArrowRight className="h-4 w-4 transition-transform duration-300 ease-out group-hover:translate-x-1" />
                </span>
              </button>
            </Link>

            <Link
              href="#features"
              className="text-background-foreground hover:text-primary-foreground underline-offset-4 hover:underline transition-all"
            >
              Learn more
            </Link>
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-background/70 to-transparent"></div>
    </section>
  );
};

export default CTA;
