import Image from "next/image";
import TestimonialsAvatars from "./TestimonialsAvatars";
import config from "@/config";
import env from "@/libs/env";
import ButtonLead from "../ButtonLead";
import saasCopy from "@/lib/saas-copy";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

const Hero = () => {
  return (
    <section
      className="relative overflow-hidden min-h-screen flex items-center justify-center bg-background"
      id="hero"
    >
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background/90"></div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
        <div className="flex flex-col items-center max-w-4xl mx-auto text-center">
          <h1 className="font-extrabold text-4xl lg:text-6xl tracking-tight mb-6 md:mb-8 text-foreground leading-tight">
            {saasCopy.hero.title}
          </h1>

          <p className="text-lg font-semibold text-muted-foreground mb-10 leading-relaxed max-w-2xl">
            {saasCopy.hero.subtitle}
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8 w-full max-w-md">
            {env.WAITLIST_MODE_FLAG ? (
              <ButtonLead />
            ) : (
              <Link href="#pricing" className="w-full sm:w-auto">
                <button className="group relative w-full overflow-hidden rounded-lg bg-primary px-8 py-4 text-primary-foreground transition-all duration-300 ease-out hover:bg-primary/90 shadow-md raise-on-hover">
                  <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                    Get {config.metadata.appName}
                    <ArrowRight className="h-4 w-4 transition-transform duration-300 ease-out group-hover:translate-x-1" />
                  </span>
                </button>
              </Link>
            )}

            <Link
              href="#features"
              className="text-foreground hover:text-primary underline-offset-4 hover:underline transition-all"
            >
              Learn more
            </Link>
          </div>

          <div className="mb-12">
            <TestimonialsAvatars priority={true} />
          </div>

          <div className="relative rounded-xl overflow-hidden bg-background/20 backdrop-blur-sm p-1 border border-primary/50 border-dashed mt-8 max-w-3xl mx-auto flex justify-center raise-on-hover shadow-lg hover:shadow-2xl ">
            <Image
              src="https://images.unsplash.com/photo-1571171637578-41bc2dd41cd2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3540&q=80"
              alt="Automated coffee chats for your Discord community"
              className="w-[600px] h-auto rounded-lg"
              width={600}
              height={400}
              priority
            />
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-background/70 to-transparent"></div>
    </section>
  );
};

export default Hero;
