import ButtonCheckout from "@/components/ButtonCheckout";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
} from "@/components/ui/card";
import { PLANS, PlanData } from "@/libs/pricing";
import { Badge } from "@/components/ui/badge";
import { CheckIcon } from "lucide-react";
import type Stripe from "stripe";

interface PricingProps {
  stripePrices: Stripe.Price[];
}

const hasStripePriceId = (
  plan: PlanData
): plan is PlanData & { stripePriceId: string } => {
  return "stripePriceId" in plan && typeof plan.stripePriceId === "string";
};

const Pricing = ({ stripePrices }: PricingProps) => {
  return (
    <section id="pricing" className="py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <p className="font-medium text-primary mb-8">Pricing</p>
          <h2 className="text-3xl font-bold lg:text-5xl">Flexible pricing</h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Choose the plan that fits your needs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {Object.entries(PLANS).map(([planId, plan]) => {
            let stripePrice: Stripe.Price | undefined | null = null;
            if (hasStripePriceId(plan)) {
              stripePrice = stripePrices.find(
                (p) => p.id === plan.stripePriceId
              );
            }

            return (
              <Card
                key={planId}
                className={`flex flex-col ${
                  planId === "pro"
                    ? "border-primary shadow-lg"
                    : "border-border"
                }`}
              >
                <CardHeader className="p-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-2xl font-bold">{plan.name}</h3>
                      <p className="text-lg font-medium text-muted-foreground mt-2">
                        {stripePrice
                          ? `$${(stripePrice.unit_amount || 0) / 100}`
                          : "$0"}
                        <span className="text-sm font-normal">
                          {stripePrice?.recurring ? "/ month" : ""}
                        </span>
                      </p>
                    </div>
                    {planId === "pro" && (
                      <Badge variant="default" className="text-sm">
                        POPULAR
                      </Badge>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="flex flex-col gap-5 flex-grow p-6">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckIcon className="h-5 w-5 flex-shrink-0 text-primary" />
                      <span className="text-sm text-muted-foreground">
                        {feature}
                      </span>
                    </div>
                  ))}
                </CardContent>

                {hasStripePriceId(plan) && (
                  <CardFooter className="p-6 mt-auto">
                    <ButtonCheckout
                      priceId={plan.stripePriceId}
                      mode="subscription"
                      planName={plan.name}
                    />
                  </CardFooter>
                )}
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Pricing;
