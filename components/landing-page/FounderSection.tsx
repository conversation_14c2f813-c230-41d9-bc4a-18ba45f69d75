import React from "react";
import Image from "next/image";
import config from "@/config";

export type FounderSectionProps = {
  embedLink: string;
  ctaText?: string;
};

export default function FounderSection({
  embedLink,
  ctaText = `Want to see how I use ${config.metadata.appName}? Check out this demo`,
}: FounderSectionProps) {
  return (
    <section className="py-16 px-8 bg-neutral text-neutral-content">
      <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
        <div className="w-full md:w-1/3">
          <Image
            src="/founder_lg.jpg"
            alt={`Will - ${config.metadata.appName} Founder`}
            width={300}
            height={300}
            className="rounded-xl shadow-lg mx-auto"
          />
        </div>
        <div className="w-full md:w-2/3 text-center md:text-left">
          <h2 className="text-3xl font-bold mb-4">hi, i&apos;m will</h2>
          <p className="text-xl mb-4">
            Growing up, I loved video games and RPGs that allowed me to grind
            out experience and level up different skills.
          </p>
          <p className="text-lg mb-6">
            I created ProgressForge as a way to break free from my own phone
            addiction and the endless pull of social media. It&apos;s designed
            to help others, like me, focus on real-life progress and channel
            their energy into meaningful skill development. By gamifying
            personal growth, ProgressForge turns everyday improvement into an
            adventure worth sharing.
          </p>
        </div>
      </div>
      <section className="relative w-full pb-24" id="demo">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-center mb-8">{ctaText}</h2>
          <div className="relative w-full max-w-4xl mx-auto">
            <div className="relative aspect-video w-full rounded-xl overflow-hidden shadow-2xl">
              <iframe
                src={embedLink}
                title={`${config.metadata.appName} Demo`}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="absolute inset-0 w-full h-full"
              ></iframe>
            </div>
          </div>
        </div>
      </section>
    </section>
  );
}
