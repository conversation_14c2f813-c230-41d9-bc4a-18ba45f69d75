"use client";

import saasCopy from "@/lib/saas-copy";
import type { JSX } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// <FAQ> component is a list of <AccordionItem> components
// Just import the FAQ & add your FAQ content to the const faqList array below.

interface FAQItemProps {
  question: string;
  answer: JSX.Element;
}

const faqList: FAQItemProps[] = [
  {
    question: "What do I get exactly?",
    answer: (
      <div className="space-y-2 leading-relaxed">
        {saasCopy.faq.whatDoIGetExactly}
      </div>
    ),
  },
  {
    question: "Can I get a refund?",
    answer: (
      <p>
        Yes! You can request a refund within 7 days of your purchase. Reach out
        by email.
      </p>
    ),
  },
  {
    question: "What payment methods do you accept?",
    answer: (
      <p>
        We accept all major credit cards, including Visa, Mastercard, and
        American Express. We also accept PayPal.
      </p>
    ),
  },
  {
    question: "How can I get access to the boilerplate?",
    answer: (
      <p>
        After your purchase, you will receive an email with a link to download
        the boilerplate. You will also get access to our private GitHub
        repository.
      </p>
    ),
  },
  {
    question: "How to get help on our issues?",
    answer: (
      <p>
        You can click on the support button at the bottom right of this screen
        and chat directly with the creator. We try to reply as soon as possible,
        often within the hour.
      </p>
    ),
  },
];

const FAQ = () => {
  return (
    <section id="faq" className="py-24 md:py-32 bg-background">
      <div className="container px-8">
        <h2 className="text-3xl font-bold tracking-tight text-center mb-12 md:mb-20">
          Frequently Asked Questions
        </h2>

        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqList.map((item, i) => (
              <AccordionItem key={i} value={`item-${i}`}>
                <AccordionTrigger className="text-left">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
