"use client";

import Link from "next/link";
import Image from "next/image";
import logo from "@/app/icon.png";
import config from "@/config";
import { ThemePicker } from "./ThemePicker";

// A header with a logo on the left, links in the center (like Pricing, etc...), and a CTA (like Get Started or Login) on the right.
// The header is responsive, and on mobile, the links are hidden behind a burger button.
const HeaderPreLaunch = () => {
  return (
    <header className="backdrop-blur-sm bg-background/90 sticky top-0 z-40 border-b border-border/40">
      <nav
        className="max-w-5xl mx-auto flex items-center justify-between px-6 py-4"
        aria-label="Global"
      >
        {/* Empty div for layout balance */}
        <div className="w-9" />

        {/* Centered logo */}
        <Link
          className="flex items-center gap-2 transition hover:opacity-80"
          href="/"
          title={`${config.metadata.appName} homepage`}
        >
          <Image
            src={logo}
            alt={`${config.metadata.appName} logo`}
            className="w-8 h-8"
            placeholder="blur"
            priority={true}
            width={32}
            height={32}
          />
          <span className="font-bold text-lg">{config.metadata.appName}</span>
        </Link>

        {/* Theme picker */}
        <ThemePicker />
      </nav>
    </header>
  );
};

export default HeaderPreLaunch;
