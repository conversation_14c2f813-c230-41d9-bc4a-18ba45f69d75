import Link from "next/link";
import { ChevronRight } from "lucide-react";

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumbs({ items, className = "" }: BreadcrumbsProps) {
  if (items.length === 0) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <nav
        className={`flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm text-muted-foreground mb-4 sm:mb-6 py-2 sm:py-3 ${className}`}
      >
        {items.map((item, index) => {
          const isLastItem = index === items.length - 1;
          return (
            <div
              key={index}
              className={`flex items-center space-x-1 sm:space-x-2 ${isLastItem ? "hidden sm:flex" : ""}`}
            >
              {index > 0 && (
                <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              )}
              {item.href ? (
                <Link
                  href={item.href}
                  className={`hover:text-foreground transition-colors truncate ${
                    isLastItem
                      ? "max-w-[120px] sm:max-w-[300px]"
                      : "max-w-[120px] sm:max-w-none"
                  }`}
                  title={item.label}
                >
                  {item.label}
                </Link>
              ) : (
                <span
                  className={`text-foreground font-medium truncate ${
                    isLastItem
                      ? "max-w-[120px] sm:max-w-[300px]"
                      : "max-w-[120px] sm:max-w-none"
                  }`}
                  title={item.label}
                >
                  {item.label}
                </span>
              )}
            </div>
          );
        })}
      </nav>
    </div>
  );
}
