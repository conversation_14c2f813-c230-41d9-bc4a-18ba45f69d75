"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { trpc } from "@/utils/trpc/client";
import { Loader2, AlertCircle } from "lucide-react";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  getCollectionIcon,
  getCollectionColorClasses,
  getCollectionColorButtonClass,
  COLLECTION_ICON_MAP,
  COLLECTION_COLOR_MAP,
} from "@/lib/collection-utils";
import { cn } from "@/lib/utils";
import { COLLECTION_LIMIT } from "@/lib/constants";

interface CollectionFormData {
  name: string;
  description: string;
  isPublic: boolean;
  icon: string;
  color: string;
}

interface CollectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collection?: {
    id: string;
    name: string;
    description?: string;
    isPublic: boolean;
    icon: string;
    color: string;
  } | null;
  onSuccess?: () => void;
  taskId?: string;
}

export function CollectionDialog({
  open,
  onOpenChange,
  collection,
  onSuccess,
  taskId,
}: CollectionDialogProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CollectionFormData>({
    defaultValues: {
      name: "",
      description: "",
      isPublic: false,
      icon: "bookmark",
      color: "blue",
    },
  });

  const utils = trpc.useUtils();
  const router = useRouter();
  const isEditing = !!collection;
  const selectedIcon = watch("icon");
  const selectedColor = watch("color");

  // Get current collections count to check limit
  const { data: collectionsCountData } =
    trpc.collections.getUserCollectionsCount.useQuery(undefined, {
      enabled: open && !isEditing, // Only check limit when creating new collections
    });

  const hasReachedLimit =
    !isEditing &&
    collectionsCountData &&
    collectionsCountData.count >= COLLECTION_LIMIT;

  const createMutation = trpc.collections.createCollection.useMutation({
    onSuccess: () => {
      toast.success("Collection created successfully!");
      utils.collections.getUserCollections.invalidate();
      utils.collections.getUserCollectionsCount.invalidate();
      router.refresh();
      onSuccess?.();
      onOpenChange(false);
      reset();
    },
    onError: (error: { message?: string }) => {
      toast.error(error.message || "Failed to create collection");
    },
  });

  const updateMutation = trpc.collections.updateCollection.useMutation({
    onSuccess: () => {
      toast.success("Collection updated successfully!");
      utils.collections.getUserCollections.invalidate();
      utils.collections.getUserCollectionsCount.invalidate();
      router.refresh();
      onSuccess?.();
      onOpenChange(false);
      reset();
    },
    onError: (error: { message?: string }) => {
      toast.error(error.message || "Failed to update collection");
    },
  });

  const isLoading = createMutation.isLoading || updateMutation.isLoading;

  // Reset form when dialog opens/closes or collection changes
  useEffect(() => {
    if (open) {
      reset({
        name: collection?.name || "",
        description: collection?.description || "",
        isPublic: collection?.isPublic || false,
        icon: collection?.icon || "bookmark",
        color: collection?.color || "blue",
      });
    }
  }, [open, collection, reset]);

  const onSubmit = async (data: CollectionFormData) => {
    if (isEditing && collection) {
      await updateMutation.mutateAsync({
        id: collection.id,
        data,
      });
    } else {
      await createMutation.mutateAsync({
        ...data,
        taskId,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <TooltipProvider>
          <DialogHeader>
            <DialogTitle>
              {isEditing ? "Edit Collection" : "Create New Collection"}
            </DialogTitle>
          </DialogHeader>

          {hasReachedLimit ? (
            <div className="py-6">
              <div className="text-center py-8 px-6 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  <span className="font-semibold text-red-800 dark:text-red-200 text-lg">
                    Collection Limit Reached
                  </span>
                </div>
                <p className="text-red-700 dark:text-red-300 mb-4 leading-relaxed">
                  You&apos;ve reached the maximum limit of{" "}
                  <strong>{COLLECTION_LIMIT} collections</strong>. To create a
                  new collection, please delete an existing one first.
                </p>
                <p className="text-sm text-red-600 dark:text-red-400 mb-6">
                  You currently have {collectionsCountData?.count || 0}{" "}
                  collections.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    className="text-red-800 dark:text-red-200 border-red-300 dark:border-red-700 hover:bg-red-100 dark:hover:bg-red-900"
                  >
                    Close
                  </Button>
                  <Button
                    onClick={() => {
                      onOpenChange(false);
                      // You could add navigation to collections page here if needed
                      // router.push('/collections');
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Manage Collections
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="name"
                    placeholder="Collection name"
                    disabled={isLoading}
                    {...register("name", {
                      required: "Collection name is required",
                      maxLength: {
                        value: 100,
                        message: "Name must be less than 100 characters",
                      },
                    })}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium">
                    Description (Optional)
                  </label>
                  <textarea
                    id="description"
                    placeholder="Collection description"
                    disabled={isLoading}
                    className="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    {...register("description", {
                      maxLength: {
                        value: 500,
                        message: "Description must be less than 500 characters",
                      },
                    })}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-500">
                      {errors.description.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Icon</label>
                  <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 gap-2">
                    {Object.keys(COLLECTION_ICON_MAP).map((iconName) => {
                      const IconComponent = getCollectionIcon(iconName);
                      const colorClasses =
                        getCollectionColorClasses(selectedColor);
                      return (
                        <button
                          key={iconName}
                          type="button"
                          onClick={() => setValue("icon", iconName)}
                          disabled={isLoading}
                          className={cn(
                            "flex items-center justify-center w-12 h-12 rounded-md border-2 transition-colors",
                            selectedIcon === iconName
                              ? "border-primary bg-primary/10"
                              : "border-border hover:border-primary/50 hover:bg-primary/5"
                          )}
                        >
                          <IconComponent
                            className={cn(
                              "w-5 h-5",
                              colorClasses.split(" ")[0]
                            )}
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Color</label>
                  <div className="grid grid-cols-5 sm:grid-cols-10 gap-2">
                    {Object.keys(COLLECTION_COLOR_MAP).map((colorName) => {
                      const colorButtonClass =
                        getCollectionColorButtonClass(colorName);
                      return (
                        <Tooltip key={colorName}>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              onClick={() => setValue("color", colorName)}
                              disabled={isLoading}
                              className={cn(
                                "w-12 h-12 rounded-md border-2 transition-colors",
                                selectedColor === colorName
                                  ? "border-primary scale-105"
                                  : "border-border hover:border-primary/50",
                                colorButtonClass
                              )}
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="capitalize">{colorName}</p>
                          </TooltipContent>
                        </Tooltip>
                      );
                    })}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPublic"
                    disabled={isLoading}
                    className="h-4 w-4 rounded border-gray-300"
                    {...register("isPublic")}
                  />
                  <label htmlFor="isPublic" className="text-sm font-medium">
                    Make this collection public
                  </label>
                </div>
              </div>

              <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {isEditing ? "Update Collection" : "Create Collection"}
                </Button>
              </DialogFooter>
            </form>
          )}
        </TooltipProvider>
      </DialogContent>
    </Dialog>
  );
}
