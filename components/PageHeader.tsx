"use client";
import { useIsMobile } from "@/hooks/use-mobile";
import React from "react";
const PageHeader = ({
  title,
  subtitle,
  actions,
  actionsInlineOnMobile = true,
}: {
  title: string | React.ReactNode;
  subtitle?: string | React.ReactNode;
  actions?: React.ReactNode;
  actionsInlineOnMobile?: boolean;
}) => {
  const isMobile = useIsMobile();
  return (
    <div className="flex flex-col w-full gap-x-2 pb-10">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl md:text-4xl font-extrabold">{title}</h1>
        {actions && (actionsInlineOnMobile || !isMobile) && (
          <div className="flex gap-2">{actions}</div>
        )}
      </div>
      {subtitle && <p className="text-md text-muted-foreground">{subtitle}</p>}
      {actions && isMobile && actionsInlineOnMobile === false && (
        <div className="flex gap-2">{actions}</div>
      )}
    </div>
  );
};

export default PageHeader;
