"use client";

import React, { useRef, useState } from "react";
import { toast } from "react-hot-toast";
import { trpc } from "@/utils/trpc/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

// This component is used to collect the emails from the landing page
// You'd use this if your product isn't ready yet or you want to collect leads
// For instance: A popup to send a freebie, joining a waitlist, etc.
// It uses tRPC to call the lead router's upsertLead mutation
const ButtonLead = ({ extraStyle }: { extraStyle?: string }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [email, setEmail] = useState<string>("");
  const [isDisabled, setIsDisabled] = useState<boolean>(false);

  // Use the tRPC mutation hook
  const upsertLeadMutation = trpc.lead.upsertLead.useMutation({
    onSuccess: () => {
      toast.success("Thanks for joining the waitlist!");

      // just remove the focus on the input
      if (inputRef.current) {
        inputRef.current.blur();
      }
      setEmail("");
      setIsDisabled(true);
    },
    onError: (error) => {
      console.log(error);
      toast.error(error.message || "Failed to join waitlist");
    },
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();

    try {
      await upsertLeadMutation.mutateAsync({ email });
    } catch (error) {
      console.error(error);
      // Error is handled in the onError callback
    }
  };
  return (
    <form
      className={cn("w-full max-w-xs space-y-3", extraStyle)}
      onSubmit={handleSubmit}
    >
      <Input
        required
        type="email"
        value={email}
        ref={inputRef}
        autoComplete="email"
        placeholder="<EMAIL>"
        className="w-full placeholder:text-muted-foreground/60"
        onChange={(e) => setEmail(e.target.value)}
      />

      <Button
        className="w-full"
        type="submit"
        disabled={isDisabled || upsertLeadMutation.isLoading}
      >
        Join waitlist
        {upsertLeadMutation.isLoading ? (
          <svg
            className="ml-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        ) : (
          <ArrowRight className="ml-2 h-4 w-4" />
        )}
      </Button>
    </form>
  );
};

export default ButtonLead;
