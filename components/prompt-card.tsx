"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { AIPromptButton } from "./ai-prompt-button";
import { CopyButton } from "./copy-button";
import { useTrackSolutionUsage } from "@/hooks/use-track-solution-usage";

interface PromptCardProps {
  name: string;
  description: string;
  prompt: string;
  solutionId: string;
  onFillAndCopy?: () => void;
}

export function PromptCard({
  name,
  description,
  prompt,
  solutionId,
  onFillAndCopy,
}: PromptCardProps) {
  const { trackUsage } = useTrackSolutionUsage();

  const handleRunClick = () => {
    trackUsage(solutionId);
  };

  const handleCopyClick = () => {
    trackUsage(solutionId);
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow border-l-4 border-l-blue-400">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3 mb-2">
          <div className="flex items-start gap-3 min-w-0 flex-1">
            <div className="min-w-0 flex-1">
              <CardTitle className="text-lg leading-tight">{name}</CardTitle>
            </div>
          </div>

          <div className="flex gap-2 shrink-0">
            <AIPromptButton
              prompt={prompt}
              className="text-xs h-7 px-2"
              onRunClick={handleRunClick}
            />
            <CopyButton
              text={prompt}
              onFillAndCopy={onFillAndCopy}
              onCopyClick={handleCopyClick}
              className="text-xs h-7 px-2"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <CardDescription className="text-sm leading-relaxed">
          {description}
        </CardDescription>
      </CardContent>
    </Card>
  );
}
