"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuthGuard } from "@/hooks/use-auth-guard";

interface SimpleCopyButtonProps {
  text: string;
  className?: string;
  onCopyClick?: () => void;
}

export function SimpleCopyButton({
  text,
  className,
  onCopyClick,
}: SimpleCopyButtonProps) {
  const [copied, setCopied] = useState(false);
  const { executeWithAuth, SignInDialog } = useAuthGuard({
    title: "Sign in to copy prompts",
    description:
      "Create a free account to copy prompts and access all features",
  });

  const handleCopy = async () => {
    executeWithAuth(async () => {
      try {
        onCopyClick?.();
        await navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Failed to copy text: ", err);
      }
    });
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCopy}
        className={cn(
          "text-muted-foreground hover:text-foreground hover:bg-muted",
          className
        )}
        aria-label="Copy to clipboard"
      >
        {copied ? (
          <>
            <Check className="h-4 w-4 text-green-600" />
            <span className="text-green-600">Copied</span>
          </>
        ) : (
          <>
            <Copy className="h-4 w-4" />
            <span>Copy</span>
          </>
        )}
      </Button>

      {/* Sign In Dialog */}
      <SignInDialog />
    </>
  );
}
