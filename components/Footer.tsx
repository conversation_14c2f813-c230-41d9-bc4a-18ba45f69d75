import Link from "next/link";
import Image from "next/image";
import config from "@/config";
import logo from "@/app/icon.png";
import env from "@/libs/env";

// Add the Footer to the bottom of your landing page and more.
// The support link is connected to the config.js file. If there's no config.resend.supportEmail, the link won't be displayed.

const Footer = () => {
  return (
    <footer className="bg-muted border-t border-border">
      <div className="max-w-7xl mx-auto px-8 py-24">
        <div className="flex lg:items-start md:flex-row md:flex-nowrap flex-wrap flex-col">
          <div className="w-64 flex-shrink-0 md:mx-0 mx-auto text-center md:text-left">
            <Link
              href="/#"
              aria-current="page"
              className="flex gap-2 justify-center md:justify-start items-center"
            >
              <Image
                src={logo}
                alt={`${config.metadata.appName} logo`}
                priority={true}
                className="w-6 h-6"
                width={24}
                height={24}
              />
              <strong className="font-extrabold tracking-tight text-base md:text-lg">
                {config.metadata.appName}
              </strong>
            </Link>

            <p className="mt-3 text-sm text-muted-foreground">
              {config.metadata.appDescription}
            </p>
            <p className="mt-3 text-sm text-muted-foreground/60">
              Copyright © {new Date().getFullYear()} - All rights reserved
            </p>
          </div>
          {env.WAITLIST_MODE_FLAG === false && (
            <div className="flex-grow flex flex-wrap justify-center -mb-10 md:mt-0 mt-10 text-center">
              <div className="lg:w-1/3 md:w-1/2 w-full px-4">
                <div className="font-semibold text-foreground tracking-widest text-sm md:text-left mb-3">
                  LINKS
                </div>

                <div className="flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm">
                  {config.resend.supportEmail && (
                    <a
                      href={`mailto:${config.resend.supportEmail}`}
                      target="_blank"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                      aria-label="Contact Support"
                    >
                      Support
                    </a>
                  )}
                  <Link
                    href="/#pricing"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Pricing
                  </Link>
                  {env.BLOG_ENABLED_FLAG && (
                    <Link
                      href="/blog"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Blog
                    </Link>
                  )}
                  <Link
                    href="https://willness.dev"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Built by Will Ness
                  </Link>
                </div>
              </div>

              <div className="lg:w-1/3 md:w-1/2 w-full px-4">
                <div className="font-semibold text-foreground tracking-widest text-sm md:text-left mb-3">
                  LEGAL
                </div>

                <div className="flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm">
                  <Link
                    href="/tos"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Terms of services
                  </Link>
                  <Link
                    href="/privacy-policy"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Privacy policy
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
