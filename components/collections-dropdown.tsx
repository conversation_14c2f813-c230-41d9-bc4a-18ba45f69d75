"use client";

import Link from "next/link";
import { BookmarkIcon, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CollectionItem } from "@/types/models/collection";

type CollectionsDropdownProps = {
  collections: CollectionItem[];
};

export default function CollectionsDropdown({
  collections,
}: CollectionsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="hidden md:flex items-center gap-2"
        >
          <BookmarkIcon className="w-4 h-4" />
          Collections
          <ChevronDown className="w-3 h-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        {collections.length > 0 ? (
          <>
            {collections.slice(0, 9).map((collection) => (
              <Link
                key={collection.id}
                href={`/collections/${collection.id}`}
                className="block"
              >
                <DropdownMenuItem className="flex flex-col items-start gap-1">
                  <span className="font-medium truncate w-full">
                    {collection.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {collection.tasks.length} task
                    {collection.tasks.length !== 1 ? "s" : ""}
                  </span>
                </DropdownMenuItem>
              </Link>
            ))}
            {collections.length > 9 && (
              <>
                <DropdownMenuSeparator />
                <Link href="/collections" className="block">
                  <DropdownMenuItem>
                    <span className="font-medium">View all collections</span>
                  </DropdownMenuItem>
                </Link>
              </>
            )}
          </>
        ) : (
          <DropdownMenuItem disabled>
            <span className="text-muted-foreground">No collections yet</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <Link href="/collections" className="block">
          <DropdownMenuItem>
            <span className="font-medium">Manage collections</span>
          </DropdownMenuItem>
        </Link>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
