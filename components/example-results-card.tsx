"use client";

import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";

interface ExampleResult {
  conversation: Array<{
    role: string;
    message: string;
  }>;
}

interface ExampleResultsCardProps {
  exampleResult: ExampleResult;
  className?: string;
}

export function ExampleResultsCard({
  exampleResult,
  className,
}: ExampleResultsCardProps) {
  const [showAllMessages, setShowAllMessages] = useState(false);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-xl">Example Result</CardTitle>
        <CardDescription>
          A sample conversation showing how this solution works
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {exampleResult.conversation.length > 1 && (
            <div className="flex justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAllMessages(!showAllMessages)}
                className="text-xs"
              >
                {showAllMessages
                  ? "Show only final message"
                  : "Load all messages"}
              </Button>
            </div>
          )}

          {(showAllMessages
            ? exampleResult.conversation
            : exampleResult.conversation.slice(-1)
          )
            .filter(Boolean)
            .map((exchange, index, _array) => {
              const isUser =
                exchange.role.toLowerCase() === "user" ||
                exchange.role.toLowerCase() === "human";
              const baseClasses = "p-4 rounded-lg border";
              const backgroundClasses = isUser
                ? "bg-muted/50 border-muted-foreground/20"
                : "bg-primary/5 border-primary/20";
              const positionClasses = showAllMessages
                ? isUser
                  ? "ml-8 mr-0"
                  : "ml-0 mr-8"
                : "mx-0";

              return (
                <div
                  key={showAllMessages ? index : "final"}
                  className={`${baseClasses} ${backgroundClasses} ${positionClasses}`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Badge
                      variant={isUser ? "outline" : "default"}
                      className="text-xs"
                    >
                      {exchange.role}
                    </Badge>
                  </div>
                  <div className="prose dark:prose-invert max-w-none prose-sm overflow-x-auto">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeRaw]}
                    >
                      {exchange.message}
                    </ReactMarkdown>
                  </div>
                </div>
              );
            })}
        </div>
      </CardContent>
    </Card>
  );
}
