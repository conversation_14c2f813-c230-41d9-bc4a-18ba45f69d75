"use client";

import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { SimpleCopyButton } from "@/components/simple-copy-button";
import { PromptCard } from "@/components/prompt-card";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useState } from "react";
import { SolutionItem } from "@/types/models/solution";
import { ExampleResultsCard } from "@/components/example-results-card";
import { useTrackSolutionUsage } from "@/hooks/use-track-solution-usage";
import { AddToCollectionDialog } from "@/components/add-to-collection-dialog";
import { ShareButton } from "@/components/share-button";
import { CollectionItem } from "@/types/models/collection";
import <PERSON>Header from "@/components/PageHeader";

function PromptSheet({
  solution,
  solutionId,
}: {
  solution: { name: string; prompt: string };
  solutionId: string;
}) {
  const { trackUsage } = useTrackSolutionUsage();

  const handleCopyClick = () => {
    trackUsage(solutionId);
  };

  // Color palette using CSS variables for proper light/dark mode support
  const colors = [
    {
      bg: "bg-chart-1/10",
      border: "border-l-chart-1",
      text: "text-chart-1",
      highlight: "bg-chart-1/20",
    },
    {
      bg: "bg-chart-3/10",
      border: "border-l-chart-3",
      text: "text-chart-3",
      highlight: "bg-chart-3/20",
    },
    {
      bg: "bg-chart-2/10",
      border: "border-l-chart-2",
      text: "text-chart-2",
      highlight: "bg-chart-2/20",
    },
    {
      bg: "bg-accent",
      border: "border-l-primary",
      text: "text-primary",
      highlight: "bg-primary/20",
    },
    {
      bg: "bg-chart-4/10",
      border: "border-l-chart-4",
      text: "text-chart-4",
      highlight: "bg-chart-4/20",
    },
    {
      bg: "bg-secondary",
      border: "border-l-secondary-foreground",
      text: "text-secondary-foreground",
      highlight: "bg-secondary-foreground/20",
    },
    {
      bg: "bg-chart-5/10",
      border: "border-l-chart-5",
      text: "text-chart-5",
      highlight: "bg-chart-5/20",
    },
    {
      bg: "bg-destructive/10",
      border: "border-l-destructive",
      text: "text-destructive",
      highlight: "bg-destructive/20",
    },
  ];

  // Extract template variables from prompt ({{ VARIABLE_NAME }})
  const extractTemplateVariables = (prompt: string): string[] => {
    const matches = Array.from(prompt.matchAll(/\{\{\s*([^}]+?)\s*\}\}/g));
    if (!matches.length) return [];
    return Array.from(
      new Set(
        matches
          .map((match) => match[1]?.trim())
          .filter((variable): variable is string => Boolean(variable))
      )
    );
  };

  const templateVars = extractTemplateVariables(solution.prompt);
  const [templateValues, setTemplateValues] = useState<Record<string, string>>(
    () =>
      templateVars.reduce((acc, variable) => ({ ...acc, [variable]: "" }), {})
  );

  // Create highlighted version of the prompt
  const createHighlightedPrompt = () => {
    let highlightedPrompt = solution.prompt;

    templateVars.forEach((variable, index) => {
      const value = templateValues[variable];
      const colorIndex = index % colors.length;
      const color = colors[colorIndex]!; // Use non-null assertion since we know the index is valid

      const pattern = new RegExp(
        `\\{\\{\\s*${variable.replace(
          /[.*+?^${}()|[\]\\]/g,
          "\\$&"
        )}\\s*\\}\\}`,
        "g"
      );

      if (!value) {
        // Replace with colored placeholder div
        highlightedPrompt = highlightedPrompt.replace(
          pattern,
          `<div class="${color.highlight} inline-block w-20 sm:w-32 h-6 rounded opacity-50"></div>`
        );
      } else {
        // Replace with highlighted span containing the value
        highlightedPrompt = highlightedPrompt.replace(
          pattern,
          `<span class="${color.highlight} px-1 py-0.5 rounded">${value}</span>`
        );
      }
    });

    return highlightedPrompt;
  };

  // Replace template variables in prompt (for the AI button)
  const processedPrompt = templateVars.reduce((currentPrompt, variable) => {
    const value = templateValues[variable];
    if (!value) return currentPrompt;

    const pattern = new RegExp(
      `\\{\\{\\s*${variable.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\s*\\}\\}`,
      "g"
    );
    return currentPrompt.replace(pattern, value);
  }, solution.prompt);

  const handleTemplateChange = (variable: string, value: string) => {
    setTemplateValues((prev) => ({ ...prev, [variable]: value }));
  };

  return (
    <SheetContent
      side="bottom"
      className="h-[90vh] sm:h-[80vh] overflow-y-auto p-0"
    >
      <div className="sticky top-0 bg-background z-10 border-b px-4 sm:px-6 py-3 sm:py-4">
        <SheetHeader className="text-center space-y-2 sm:space-y-3">
          <SheetTitle className="text-base sm:text-lg">
            {solution.name}
          </SheetTitle>
          <div className="flex justify-center gap-2">
            <SimpleCopyButton
              text={processedPrompt}
              className="text-xs sm:text-sm h-7 sm:h-8 px-2 sm:px-3"
              onCopyClick={handleCopyClick}
            />
          </div>
        </SheetHeader>
      </div>

      <div className="flex-1 px-4 sm:px-6 py-4 sm:py-6">
        {/* Mobile: Use tabs for better UX */}
        <div className="block lg:hidden">
          <Tabs
            defaultValue={templateVars.length > 0 ? "variables" : "preview"}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-4">
              {templateVars.length > 0 && (
                <TabsTrigger value="variables">Variables</TabsTrigger>
              )}
              <TabsTrigger value="preview">
                {templateVars.length > 0 ? "Preview" : "Prompt"}
              </TabsTrigger>
            </TabsList>

            {templateVars.length > 0 && (
              <TabsContent value="variables" className="space-y-4">
                <h4 className="text-sm font-medium text-muted-foreground border-b border-border pb-2">
                  Template Variables
                </h4>
                <div className="space-y-4 max-h-[50vh] overflow-y-auto">
                  {templateVars.map((variable, index) => {
                    const colorIndex = index % colors.length;
                    const color = colors[colorIndex]!; // Use non-null assertion since we know the index is valid
                    return (
                      <div
                        key={variable}
                        className={`${color.bg} ${color.border} border-l-4 border border-border rounded-lg p-3 sm:p-4 shadow-sm`}
                      >
                        <label
                          htmlFor={variable}
                          className={`block text-sm font-medium ${color.text} mb-2`}
                        >
                          {variable.length > 30
                            ? variable.substring(0, 30) + "..."
                            : variable
                                .replace(/_/g, " ")
                                .toLowerCase()
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </label>
                        <textarea
                          id={variable}
                          value={templateValues[variable]}
                          onChange={(e) =>
                            handleTemplateChange(variable, e.target.value)
                          }
                          placeholder={`Enter ${variable
                            .toLowerCase()
                            .replace(/_/g, " ")}`}
                          rows={3}
                          className="w-full px-3 py-2 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-y min-h-[80px] bg-background"
                        />
                      </div>
                    );
                  })}
                </div>
              </TabsContent>
            )}

            <TabsContent value="preview" className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground border-b border-border pb-2">
                {templateVars.length > 0
                  ? "Generated Prompt"
                  : "Prompt Preview"}
              </h4>
              <div className="overflow-y-auto max-h-[50vh]">
                <div
                  className="whitespace-pre-wrap bg-muted p-4 rounded-lg text-sm border"
                  dangerouslySetInnerHTML={{
                    __html: createHighlightedPrompt(),
                  }}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Desktop: Side-by-side layout */}
        <div className="hidden lg:block">
          <div className="grid grid-cols-3 gap-8 h-full">
            {/* Left side - Template Variables */}
            <div className="space-y-4">
              {templateVars.length > 0 ? (
                <>
                  <h4 className="text-sm font-medium text-muted-foreground border-b border-border pb-2">
                    Template Variables
                  </h4>
                  <div className="space-y-4 max-h-[calc(80vh-200px)] overflow-y-auto">
                    {templateVars.map((variable, index) => {
                      const colorIndex = index % colors.length;
                      const color = colors[colorIndex]!; // Use non-null assertion since we know the index is valid
                      return (
                        <div
                          key={variable}
                          className={`${color.bg} ${color.border} border-l-4 border border-border rounded-lg p-3 sm:p-4 shadow-sm`}
                        >
                          <label
                            htmlFor={variable}
                            className={`block text-sm font-medium ${color.text} mb-2`}
                          >
                            {variable.length > 30
                              ? variable.substring(0, 30) + "..."
                              : variable
                                  .replace(/_/g, " ")
                                  .toLowerCase()
                                  .replace(/\b\w/g, (l) => l.toUpperCase())}
                          </label>
                          <textarea
                            id={variable}
                            value={templateValues[variable]}
                            onChange={(e) =>
                              handleTemplateChange(variable, e.target.value)
                            }
                            placeholder={`Enter ${variable
                              .toLowerCase()
                              .replace(/_/g, " ")}`}
                            rows={3}
                            className="w-full px-3 py-2 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-y min-h-[80px] bg-background"
                          />
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : (
                <div className="text-center text-muted-foreground mt-20">
                  <p className="text-sm">No template variables found</p>
                  <p className="text-xs mt-1">
                    This prompt is ready to use as-is
                  </p>
                </div>
              )}
            </div>

            {/* Right side - Preview */}
            <div className="space-y-4 col-span-2">
              <h4 className="text-sm font-medium text-muted-foreground border-b border-border pb-2">
                {templateVars.length > 0
                  ? "Generated Prompt"
                  : "Prompt Preview"}
              </h4>
              <div className="overflow-y-auto max-h-[calc(80vh-200px)]">
                <div
                  className="whitespace-pre-wrap bg-muted p-6 rounded-lg text-sm border"
                  dangerouslySetInnerHTML={{
                    __html: createHighlightedPrompt(),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </SheetContent>
  );
}

interface SolutionPageViewProps {
  solution: SolutionItem;
  taskId: string | null;
  collections: CollectionItem[] | null;
}

export function SolutionPageView({
  solution,
  taskId,
  collections,
}: SolutionPageViewProps) {
  const [openSheet, setOpenSheet] = useState(false);

  const handleFillAndCopy = () => {
    setOpenSheet(true);
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      <PageHeader
        title={solution.name}
        subtitle={solution.description}
        actionsInlineOnMobile={false}
        actions={
          <>
            <ShareButton
              url={`${typeof window !== "undefined" ? window.location.origin : ""}/solutions/${solution.id}`}
              title={`${solution.name} - AI Solution`}
              message={`🤖 Check out this AI solution to automate your work!

⚡ ${solution.name}

${solution.description}

${typeof window !== "undefined" ? window.location.origin : ""}/solutions/${solution.id}`}
              variant="outline"
            />
            <AddToCollectionDialog
              taskId={taskId}
              solutionName={solution.name}
              collections={collections}
            />
          </>
        }
      />

      <div className="space-y-6">
        {/* Prompt Card */}
        <div>
          <Sheet open={openSheet} onOpenChange={setOpenSheet}>
            <SheetTrigger asChild>
              <div style={{ display: "none" }} />
            </SheetTrigger>
            <PromptSheet solution={solution} solutionId={solution.id} />
          </Sheet>

          <PromptCard
            name={solution.name}
            description={solution.description}
            prompt={solution.prompt}
            solutionId={solution.id}
            onFillAndCopy={handleFillAndCopy}
          />
        </div>

        {/* Inputs Section */}
        {solution.inputs && solution.inputs.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Required Inputs</CardTitle>
              <CardDescription>
                Information you need to provide for this solution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {solution.inputs.map((input, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Badge variant="outline" className="mt-0.5 text-xs">
                      {index + 1}
                    </Badge>
                    <span className="text-sm leading-relaxed">{input}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Outputs Section */}
        {solution.outputs && solution.outputs.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Expected Outputs</CardTitle>
              <CardDescription>
                What you can expect to receive from this solution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {solution.outputs.map((output, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Badge variant="outline" className="mt-0.5 text-xs">
                      {index + 1}
                    </Badge>
                    <span className="text-sm leading-relaxed">{output}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Example Result Section */}
        {solution.example_result && (
          <ExampleResultsCard exampleResult={solution.example_result} />
        )}
      </div>
    </div>
  );
}
