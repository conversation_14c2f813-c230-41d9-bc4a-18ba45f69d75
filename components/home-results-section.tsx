"use client";

import { trpc } from "@/utils/trpc/client";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { UnifiedSearchResult } from "@/types/models/search";
import { TaskResultCard } from "@/components/task-result-card";

interface HomeResultsSectionProps {
  searchResults?: UnifiedSearchResult[] | null;
  isSearching?: boolean;
  searchQuery?: string;
  searchTotalCount?: number;
  isFetchingNextPage?: boolean;
}

export function HomeResultsSection({
  searchResults,
  isSearching = false,
  searchQuery = "",
  searchTotalCount = 0,
  isFetchingNextPage = false,
}: HomeResultsSectionProps) {
  const { data: roles, error: rolesError } = trpc.content.getRoles.useQuery();

  // Function to get role name by role ID
  const getRoleName = (roleId: string) => {
    if (!roles) return "Unknown";
    const role = roles.find((r) => r.id === roleId);
    return role?.roleName || "Unknown";
  };

  if (rolesError) {
    return (
      <div className="border-t bg-muted/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Failed to load content. Please try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-t bg-muted/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h2 className="text-2xl sm:text-3xl font-bold mb-2">
            {isSearching ? (
              <Skeleton className="h-8 w-32" />
            ) : searchQuery ? (
              `${searchTotalCount} Search Results`
            ) : (
              `${searchTotalCount} Results`
            )}
          </h2>
          <p className="text-muted-foreground">
            {searchQuery
              ? `AI solutions matching "${searchQuery}"`
              : "Ready-to-use AI solutions for your specific needs"}
          </p>
        </div>

        {/* Task Cards */}
        <div className="space-y-4">
          {(isSearching && (!searchResults || searchResults.length === 0)) ||
          (!roles && !rolesError)
            ? // Loading skeletons (show during search loading OR while roles are loading)
              Array.from({ length: 6 }).map((_, i) => {
                // Add some variation to skeleton widths for more realistic loading
                const titleWidths = ["w-3/4", "w-2/3", "w-4/5", "w-5/6"];
                const descriptionWidths = [
                  ["w-full", "w-5/6", "w-4/6"],
                  ["w-11/12", "w-3/4", "w-1/2"],
                  ["w-full", "w-4/5", "w-3/5"],
                ];
                const titleWidth = titleWidths[i % titleWidths.length]!;
                const descWidths =
                  descriptionWidths[i % descriptionWidths.length]!;

                return (
                  <Card key={i} className="border-2">
                    <CardHeader className="pb-4">
                      <Skeleton className={`h-6 ${titleWidth} mb-2`} />
                      <div className="flex items-center gap-2 mt-2 flex-wrap">
                        <Skeleton className="h-5 w-16" />
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2 mb-4">
                        <Skeleton className={`h-4 ${descWidths[0]!}`} />
                        <Skeleton className={`h-4 ${descWidths[1]!}`} />
                        <Skeleton className={`h-4 ${descWidths[2]!}`} />
                      </div>
                      <div className="flex justify-end">
                        <Skeleton className="h-6 w-24" />
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            : // Actual task cards using the new TaskResultCard component
              searchResults?.map((result) => (
                <TaskResultCard
                  key={result.task.id}
                  result={result}
                  getRoleName={getRoleName}
                />
              ))}

          {/* Loading skeleton for next page */}
          {isFetchingNextPage && (
            <Card className="border-2">
              <CardHeader className="pb-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex items-center gap-2 mt-2 flex-wrap">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 mb-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-5/6" />
                  <Skeleton className="h-4 w-4/6" />
                </div>
                <div className="flex justify-end">
                  <Skeleton className="h-6 w-24" />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Empty state for no results */}
        {!isSearching && searchResults && searchResults.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {searchQuery
                ? `No results found for "${searchQuery}"`
                : "No tasks found. Try adjusting your filters."}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
