"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Play, ChevronDown, Check, Copy, ExternalLink } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  INTERACTIVE_PROMPT_PREFIX,
  AI_PLATFORMS,
  STORAGE_KEY,
  hasPlaceholders,
  type AIPlatform,
} from "@/lib/constants";
import { useAuthGuard } from "@/hooks/use-auth-guard";

interface AIPromptButtonProps {
  prompt: string;
  className?: string;
  onRunClick?: () => void;
}

const PROVIDER_PREFERENCE_EVENT = "providerPreferenceChanged";

// Type guard to check if platform is copy enforced
const isCopyEnforced = (
  platform: AIPlatform
): platform is AIPlatform & { copyEnforced: true } => {
  return "copyEnforced" in platform;
};

export function AIPromptButton({
  prompt,
  className = "",
  onRunClick,
}: AIPromptButtonProps) {
  const [selectedProvider, setSelectedProvider] = useState<AIPlatform>(
    AI_PLATFORMS[0] as AIPlatform
  );
  const [showCopyModal, setShowCopyModal] = useState(false);
  const [modalPlatform, setModalPlatform] = useState<AIPlatform | null>(null);
  const [copied, setCopied] = useState(false);
  const [hasCopied, setHasCopied] = useState(false);

  const { executeWithAuth, SignInDialog } = useAuthGuard({
    title: "Sign in to run prompts",
    description:
      "Create a free account to run AI prompts and access all features",
  });

  // Determine if this is a compact/modal context based on className
  const isCompact = className.includes("h-7") || className.includes("text-xs");

  // Load saved preference from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const savedProvider = AI_PLATFORMS.find((p) => p.id === saved);
        if (savedProvider) {
          setSelectedProvider(savedProvider as AIPlatform);
        }
      }
    } catch (error) {
      // Handle localStorage errors gracefully
      console.warn("Failed to load AI provider preference:", error);
    }
  }, []);

  // Listen for provider changes from other AI prompt buttons
  useEffect(() => {
    const handleProviderChange = (event: CustomEvent) => {
      const newProviderId = event.detail.providerId;
      const newProvider = AI_PLATFORMS.find((p) => p.id === newProviderId);
      if (newProvider) {
        setSelectedProvider(newProvider);
      }
    };

    window.addEventListener(
      PROVIDER_PREFERENCE_EVENT,
      handleProviderChange as EventListener
    );

    return () => {
      window.removeEventListener(
        PROVIDER_PREFERENCE_EVENT,
        handleProviderChange as EventListener
      );
    };
  }, []);

  const handleProviderSelect = (platform: AIPlatform) => {
    setSelectedProvider(platform);

    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, platform.id);
    } catch (error) {
      console.warn("Failed to save AI provider preference:", error);
    }

    // Notify other AI prompt buttons on the page
    const event = new CustomEvent(PROVIDER_PREFERENCE_EVENT, {
      detail: { providerId: platform.id },
    });
    window.dispatchEvent(event);
  };

  const handleCopy = async (textToCopy: string) => {
    try {
      await navigator.clipboard.writeText(textToCopy);
      setCopied(true);
      setHasCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const openInProvider = (platform: AIPlatform) => {
    if (isCopyEnforced(platform)) {
      // Handle copy enforced platforms
      setModalPlatform(platform);
      setHasCopied(false); // Reset copy state when opening modal
      setShowCopyModal(true);
      return;
    }

    let finalPrompt = prompt;

    // If prompt has placeholders, use interactive prompt
    if (hasPlaceholders(prompt)) {
      finalPrompt = INTERACTIVE_PROMPT_PREFIX + prompt;
    }

    const encodedPrompt = encodeURIComponent(finalPrompt);
    const url = `${platform.url}${encodedPrompt}`;
    window.open(url, "_blank", "noopener,noreferrer");
  };

  const goToPlatform = (platform: AIPlatform) => {
    window.open(platform.url, "_blank", "noopener,noreferrer");
  };

  const handlePrimaryAction = () => {
    executeWithAuth(() => {
      onRunClick?.();
      openInProvider(selectedProvider);
    });
  };

  // Create base button classes
  const baseButtonClasses = isCompact
    ? "h-7 px-2 text-xs gap-1"
    : "h-8 px-3 text-sm gap-1.5";

  return (
    <>
      <div className={`flex max-w-full ${isCompact ? "text-xs" : ""}`}>
        {/* Primary button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePrimaryAction}
          className={`rounded-r-none border-r border-border text-muted-foreground hover:text-foreground hover:bg-muted flex-1 min-w-0 ${baseButtonClasses} ${className}`}
          aria-label={`Run in ${selectedProvider.name}`}
        >
          <Play className={`shrink-0 ${isCompact ? "h-3 w-3" : "h-4 w-4"}`} />
          <span className="truncate">Run in {selectedProvider.name}</span>
        </Button>

        {/* Dropdown for provider selection */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`rounded-l-none text-muted-foreground hover:text-foreground hover:bg-muted border-l-0 shrink-0 ${baseButtonClasses} px-1.5`}
              aria-label="Select AI Provider"
            >
              <ChevronDown
                className={`${isCompact ? "h-3 w-3" : "h-3.5 w-3.5"}`}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Choose Provider
            </div>
            <DropdownMenuSeparator />
            {AI_PLATFORMS.map((platform) => (
              <DropdownMenuItem
                key={platform.id}
                onClick={() => {
                  handleProviderSelect(platform);
                  executeWithAuth(() => {
                    onRunClick?.();
                    openInProvider(platform);
                  });
                }}
                className="cursor-pointer flex items-center justify-between py-2"
              >
                <div className="flex items-center">
                  <span className="font-medium">{platform.name}</span>
                  {isCopyEnforced(platform) && (
                    <span className="ml-2 text-xs text-muted-foreground">
                      (Copy)
                    </span>
                  )}
                </div>
                {selectedProvider.id === platform.id && (
                  <Check className="h-4 w-4 text-blue-600" />
                )}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              {hasPlaceholders(prompt)
                ? "Interactive prompts include AI guidance"
                : "Your selection is saved automatically"}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Copy Modal for platforms that don't support linking */}
      <Dialog
        open={showCopyModal}
        onOpenChange={(open) => {
          setShowCopyModal(open);
          if (!open) {
            setHasCopied(false); // Reset copy state when modal closes
          }
        }}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {modalPlatform?.name} doesn&apos;t support linking
            </DialogTitle>
            <DialogDescription>
              This platform doesn&apos;t support direct links. Copy the prompt
              and paste it manually into {modalPlatform?.name}.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              onClick={() => {
                let finalPrompt = prompt;
                if (hasPlaceholders(prompt)) {
                  finalPrompt = INTERACTIVE_PROMPT_PREFIX + prompt;
                }
                handleCopy(finalPrompt);
              }}
              className="flex-1"
              disabled={copied}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Prompt
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => modalPlatform && goToPlatform(modalPlatform)}
              disabled={!hasCopied}
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Go to {modalPlatform?.name}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Sign In Dialog */}
      <SignInDialog />
    </>
  );
}
