"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ShareIcon, LinkIcon, CheckIcon, MailIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ShareButtonProps {
  url: string;
  title: string;
  message: string;
  className?: string;
  variant?: "default" | "outline" | "ghost";
  showText?: boolean;
}

interface ShareOption {
  name: string;
  icon: string;
  color: string;
  getUrl: (url: string, title: string, description?: string) => string;
  action?: (url: string, title: string, description?: string) => Promise<void>;
}

export function ShareButton({
  url,
  title,
  message,
  className,
  variant = "outline",
  showText = true,
}: ShareButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [justCopied, setJustCopied] = useState(false);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setJustCopied(true);
      setTimeout(() => setJustCopied(false), 2000);
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
      // Fallback: show the URL in an alert
      alert(`Copy this link to share: ${url}`);
    }
  };

  const shareOptions: ShareOption[] = [
    {
      name: "Copy Link",
      icon: "copy",
      color: justCopied ? "text-[hsl(var(--chart-3))]" : "text-foreground",
      getUrl: () => "",
      action: handleCopyLink,
    },
    {
      name: "X (Twitter)",
      icon: "twitter",
      color: "text-foreground",
      getUrl: (url, title, _desc) => {
        const tweetText = message || title;
        return `https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`;
      },
    },
    {
      name: "Email",
      icon: "email",
      color: "text-foreground",
      getUrl: (url, title, _desc) => {
        return `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(message)}`;
      },
    },
  ];

  const handleShare = (option: ShareOption) => {
    if (option.action) {
      option.action(url, title, message);
    } else {
      const shareUrl = option.getUrl(url, title, message);
      window.open(shareUrl, "_blank", "width=600,height=400");
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variant}
          className={cn("text-xs sm:text-sm px-2 sm:px-3", className)}
        >
          {justCopied ? (
            <CheckIcon
              className={cn(
                "w-3 h-3 sm:w-4 sm:h-4",
                showText && "mr-1 sm:mr-2"
              )}
            />
          ) : (
            <ShareIcon
              className={cn(
                "w-3 h-3 sm:w-4 sm:h-4",
                showText && "mr-1 sm:mr-2"
              )}
            />
          )}
          {showText && (justCopied ? "Copied!" : "Share")}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-2" align="end">
        <div className="space-y-1">
          <div className="px-2 py-1 text-sm font-medium text-muted-foreground border-b">
            Share this{" "}
            {title.toLowerCase().includes("collection")
              ? "collection"
              : "content"}
          </div>
          {shareOptions.map((option) => (
            <button
              key={option.name}
              onClick={() => handleShare(option)}
              className="w-full flex items-center space-x-3 px-2 py-2 text-sm hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
            >
              <div className="w-5 h-5 flex items-center justify-center">
                {option.icon === "copy" ? (
                  justCopied ? (
                    <CheckIcon className="w-4 h-4 text-[hsl(var(--chart-3))]" />
                  ) : (
                    <LinkIcon className="w-4 h-4 text-muted-foreground" />
                  )
                ) : option.icon === "twitter" ? (
                  <svg
                    className="w-5 h-5 text-foreground"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                ) : option.icon === "email" ? (
                  <MailIcon className="w-4 h-4 text-muted-foreground" />
                ) : (
                  <span className="text-lg">{option.icon}</span>
                )}
              </div>
              <span className={cn("flex-1 text-left", option.color)}>
                {option.name === "Copy Link" && justCopied
                  ? "Copied!"
                  : option.name}
              </span>
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
