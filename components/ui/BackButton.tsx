"use client";

import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button, ButtonProps } from "@/components/ui/button";

export default function BackButton({ ...props }: ButtonProps) {
  const router = useRouter();

  return (
    <Button
      onClick={() => router.back()}
      variant="outline"
      data-tooltip-id="tooltip"
      data-tooltip-content="Go Back"
      {...props}
    >
      <ArrowLeft size={24} />
    </Button>
  );
}
