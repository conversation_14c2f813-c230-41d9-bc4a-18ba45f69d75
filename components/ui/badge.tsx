import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        prompt:
          "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
        managed:
          "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        workflow:
          "border-transparent bg-purple-100 text-purple-800 hover:bg-purple-200",
        success:
          "border-transparent bg-chart-3/20 text-chart-3 hover:bg-chart-3/30",
        warning:
          "border-transparent bg-chart-4/20 text-chart-4 hover:bg-chart-4/30",
        info: "border-transparent bg-chart-2/20 text-chart-2 hover:bg-chart-2/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
