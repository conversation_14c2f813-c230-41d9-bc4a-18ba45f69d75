import React from "react";
import {
  Calendar,
  Home,
  Inbox,
  Search,
  Settings,
  Users,
  Bar<PERSON><PERSON>,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import logo from "@/app/icon.png";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
} from "@/components/ui/sidebar";
import ButtonAccount from "./ButtonAccount";
import config from "@/config";
import { SidebarMenuNavigationButton } from "./SidebarMenuNavigationButton";
import { ThemePicker } from "./ThemePicker";

// Menu items.
type MenuItem = {
  title: string;
  url: string;
  icon: React.FC<{ className?: string }>;
  badge?: number;
};

type MenuGroup = {
  title: string | undefined;
  items: MenuItem[];
};

const menuGroups: MenuGroup[] = [
  {
    title: "Application",
    items: [
      {
        title: "Dashboard",
        url: "/home",
        icon: Home,
      },
      {
        title: "Inbox",
        url: "/home2",
        icon: Inbox,
        badge: 3,
      },
      {
        title: "Calendar",
        url: "#",
        icon: Calendar,
      },
      {
        title: "Analytics",
        url: "#",
        icon: BarChart,
      },
      {
        title: "Team",
        url: "#",
        icon: Users,
      },
    ],
  },
  {
    title: "Settings",
    items: [
      {
        title: "Search",
        url: "#",
        icon: Search,
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings,
      },
    ],
  },
];

export function AppSidebar() {
  return (
    <Sidebar className="border-sidebar-border shadow-sm">
      <div className="absolute top-1 left-1 z-10">
        <ThemePicker />
      </div>
      <SidebarHeader className="mb-2 pt-6">
        <div className="w-full flex gap-3 items-center justify-center">
          <Image
            src={logo}
            alt={`${config.metadata.appName} logo`}
            className="w-[36px] h-[36px] rounded-md"
            placeholder="blur"
            priority={true}
          />
          <Link href="/" className="font-bold text-xl text-sidebar-foreground">
            {config.metadata.appName}
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2">
        <div className="flex-grow space-y-4">
          {menuGroups.map(({ title, items }) => (
            <SidebarGroup key={title} className="pb-2">
              <SidebarGroupLabel className="text-xs uppercase tracking-wider text-sidebar-foreground/70 px-3 mb-1">
                {title || ""}
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {items.map((item, i) => (
                    <SidebarMenuNavigationButton
                      title={item.title}
                      url={item.url}
                      key={i}
                      badge={item.badge}
                      className="hover:bg-sidebar-accent rounded-md data-[active=true]:bg-sidebar-primary/10"
                    >
                      <item.icon className="h-5 w-5" />
                    </SidebarMenuNavigationButton>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </div>
        <SidebarGroup className="w-full py-3 mt-auto border-t border-sidebar-border relative">
          <div className="relative z-40 w-full flex justify-center">
            <ButtonAccount />
          </div>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
