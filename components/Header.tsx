"use client";

import Link from "next/link";
import Image from "next/image";
import ButtonSignin from "./ButtonSignin";
import logo from "@/app/icon.png";
import config from "@/config";
import { <PERSON><PERSON> } from "./ui/button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger, SheetClose } from "./ui/sheet";
import { Menu } from "lucide-react";
import { ThemePicker } from "./ThemePicker";

const links: {
  href: string;
  label: string;
}[] = [
  {
    href: "/#pricing",
    label: "Pricing",
  },
  // {
  //   href: "/#testimonials",
  //   label: "Reviews",
  // },
  {
    href: "/#faq",
    label: "FAQ",
  },
];

const cta: JSX.Element = <ButtonSignin />;

// A header with a logo on the left, links in the center (like Pricing, etc...), and a CTA (like Get Started or Login) on the right.
const Header = () => {
  return (
    <>
      <header className="backdrop-blur-sm bg-background/80 sticky top-0 z-40 border-b w-full">
        <div className="max-w-5xl mx-auto flex items-center justify-between px-4 sm:px-6 py-3">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link
              className="flex items-center gap-2"
              href="/"
              title={`${config.metadata.appName} homepage`}
            >
              <Image
                src={logo}
                alt={`${config.metadata.appName} logo`}
                className="w-7 h-7 sm:w-8 sm:h-8"
                placeholder="blur"
                priority={true}
                width={32}
                height={32}
              />
              <span className="font-bold text-base sm:text-lg">
                {config.metadata.appName}
              </span>
            </Link>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center gap-8">
            <nav className="flex items-center gap-6">
              {links.map((link) => (
                <Link
                  href={link.href}
                  key={link.href}
                  className="text-muted-foreground hover:text-foreground transition font-medium"
                >
                  {link.label}
                </Link>
              ))}
            </nav>
            <div className="flex items-center gap-2">
              <ThemePicker variant="lg" />
              {cta}
            </div>
          </div>

          {/* Mobile menu */}
          <Sheet>
            <div className="flex items-center gap-2 md:hidden">
              <ThemePicker variant="lg" />
              <SheetTrigger asChild className="md:hidden">
                <Button variant="ghost" size="icon">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
            </div>
            <SheetContent side="right" className="w-64 sm:max-w-sm">
              <div className="flex flex-col gap-4 mt-8">
                {links.map((link) => (
                  <SheetClose asChild key={link.href}>
                    <Link
                      href={link.href}
                      className="text-foreground hover:text-primary py-2 font-medium text-lg"
                    >
                      {link.label}
                    </Link>
                  </SheetClose>
                ))}
              </div>

              <div className="mt-4">
                <SheetClose asChild>
                  <div>{cta}</div>
                </SheetClose>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>
    </>
  );
};

export default Header;
