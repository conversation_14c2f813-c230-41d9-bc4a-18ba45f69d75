"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { CollectionDialog } from "@/components/collection-dialog";
import { ShareButton } from "@/components/share-button";
import { trpc } from "@/utils/trpc/client";
import { useRouter } from "next/navigation";
import { EditIcon, TrashIcon } from "lucide-react";

interface CollectionActionsProps {
  collection: {
    id: string;
    name: string;
    description?: string;
    tasks: {
      id: string;
      taskName: string;
      taskDescription: string;
      roles: { id: string; roleName: string; roleSlug: string }[];
      prompts: { promptCraftV4?: string };
      updatedAt: string;
      createdAt: string;
    }[];
    updatedAt: string;
    createdAt: string;
    isPublic: boolean;
    icon: string;
    color: string;
  };
  isOwner?: boolean;
}

export function CollectionActions({
  collection,
  isOwner = false,
}: CollectionActionsProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const deleteCollectionMutation =
    trpc.collections.deleteCollection.useMutation({
      onSuccess: () => {
        router.push("/collections");
      },
      onError: (error) => {
        console.error("Delete collection error:", error);
        setIsDeleting(false);
      },
    });

  const handleDelete = async () => {
    if (
      !confirm(
        "Are you sure you want to delete this collection? This action cannot be undone."
      )
    ) {
      return;
    }

    setIsDeleting(true);
    deleteCollectionMutation.mutate({ id: collection.id });
  };

  const handleEditSuccess = () => {
    router.refresh();
  };

  return (
    <>
      <div className="flex items-center gap-1 sm:gap-2">
        {collection.isPublic && (
          <ShareButton
            url={`${typeof window !== "undefined" ? window.location.origin : ""}/collections/${collection.id}`}
            title={`${collection.name} - AI Automation Collection`}
            message={`🚀 Check out this collection of AI solutions to automate common tasks!

📚 ${collection.name}

${typeof window !== "undefined" ? window.location.origin : ""}/collections/${
              collection.id
            }`}
            variant="outline"
          />
        )}

        {isOwner && (
          <>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(true)}
              disabled={isDeleting}
              className="text-xs sm:text-sm px-2 sm:px-3"
            >
              <EditIcon className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              Edit
            </Button>
            <Button
              variant="outline"
              onClick={handleDelete}
              disabled={isDeleting}
              className="text-xs sm:text-sm px-2 sm:px-3"
            >
              <TrashIcon className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </>
        )}
      </div>

      {isOwner && (
        <CollectionDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          collection={collection}
          onSuccess={handleEditSuccess}
        />
      )}
    </>
  );
}
