"use client";

import Link from "next/link";
import Image from "next/image";
import AuthButton from "./auth-button";
import config from "@/config";
import { ThemePicker } from "./ThemePicker";
import CollectionsDropdown from "./collections-dropdown";
import { CollectionItem } from "@/types/models/collection";
import { useSession } from "next-auth/react";

type AppHeaderProps = {
  collections: CollectionItem[];
};

export default function AppHeader({ collections }: AppHeaderProps) {
  const { data: session } = useSession();
  return (
    <header className="backdrop-blur-sm bg-background/80 sticky top-0 z-40 border-b w-full">
      <div className="max-w-7xl mx-auto flex items-center justify-between px-4 sm:px-6 py-3">
        {/* Logo */}
        <div className="flex-shrink-0">
          <Link
            className="flex items-center gap-2"
            href="/"
            title={`${config.metadata.appName} homepage`}
          >
            <Image
              src={"/logoTransparent.png"}
              alt={`${config.metadata.appName} logo`}
              className="w-7 h-7 sm:w-8 sm:h-8"
              priority={true}
              width={32}
              height={32}
            />
            <span className="font-bold text-base sm:text-lg">
              {config.metadata.appName}
            </span>
          </Link>
        </div>

        {/* Right side actions */}
        <div className="flex items-center gap-3">
          {/* Collections dropdown - only shows if user is logged in */}
          {session?.user && <CollectionsDropdown collections={collections} />}
          <ThemePicker variant="lg" />
          <AuthButton />
        </div>
      </div>
    </header>
  );
}
