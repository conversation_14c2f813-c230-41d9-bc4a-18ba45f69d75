"use client";

import { trpc } from "@/utils/trpc/client";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// This component is used to create Stripe Checkout Sessions
// It calls the stripe.createCheckout tRPC procedure with the priceId, successUrl and cancelUrl
// By default, it doesn't force users to be authenticated. But if they are, it will prefill the Checkout data with their email and/or credit card
// You can also change the mode to "subscription" if you want to create a subscription instead of a one-time payment
const ButtonCheckout = ({
  priceId,
  mode = "payment",
  className,
  planName,
  isChange,
}: {
  priceId: string;
  mode?: "payment" | "subscription";
  className?: string;
  planName: string;
  isChange?: boolean;
}) => {
  const router = useRouter();

  // Use the tRPC mutation
  const createCheckoutMutation = trpc.stripe.createCheckout.useMutation({
    onSuccess: (data) => {
      // Redirect to the Stripe checkout URL
      if (data.redirect) {
        router.push(data.redirect);
      } else if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error("No redirect or url found");
      }
    },
    onError: (error) => {
      console.error("Failed to create checkout session:", error);
    },
  });

  const handlePayment = async () => {
    const removePricingUrl = window.location.href.replace("#pricing", "");

    createCheckoutMutation.mutate({
      priceId,
      successUrl: removePricingUrl,
      cancelUrl: window.location.href,
      mode,
    });
  };

  const buttonText = isChange ? `Change to ${planName}` : `Buy ${planName}`;

  return (
    <Button
      className={cn("w-full", className)}
      onClick={handlePayment}
      disabled={createCheckoutMutation.isLoading}
    >
      {createCheckoutMutation.isLoading ? "Loading..." : buttonText}
    </Button>
  );
};

export default ButtonCheckout;
