"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CollectionDialog } from "@/components/collection-dialog";
import { Badge } from "@/components/ui/badge";
import { CollectionData } from "@/types/models/collection";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import PageHeader from "@/components/PageHeader";
import {
  getCollectionIcon,
  getCollectionColorClasses,
} from "@/lib/collection-utils";
import { cn } from "@/lib/utils";

interface CollectionsPageClientProps {
  collections: CollectionData[];
}

export function CollectionsPageClient({
  collections,
}: CollectionsPageClientProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <div className="w-full py-6">
      <PageHeader
        title="Collections"
        subtitle="Organize your favorite tasks into collections"
        actions={
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            Create Collection
          </Button>
        }
        actionsInlineOnMobile={false}
      />

      <CollectionsGrid collections={collections} />

      <CollectionDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
    </div>
  );
}

interface CollectionsGridProps {
  collections: CollectionData[];
}

function CollectionsGrid({ collections }: CollectionsGridProps) {
  // Empty State
  if (collections.length === 0) {
    const DefaultIcon = getCollectionIcon("bookmark");

    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <DefaultIcon className="w-12 h-12 text-blue-600 mb-4" />
          <CardTitle className="text-xl mb-2">No collections yet</CardTitle>
          <p className="text-muted-foreground text-center mb-6 max-w-md">
            Create your first collection to start organizing your favorite
            tasks. Collections help you group related tasks for easy access.
          </p>
          <CreateCollectionButton showCreateButton />
        </CardContent>
      </Card>
    );
  }

  // Collections Grid
  return (
    <div className="grid grid-cols-1 gap-6">
      {collections.map((collection) => (
        <CollectionCardWrapper key={collection.id} collection={collection} />
      ))}
    </div>
  );
}

interface CreateCollectionButtonProps {
  showCreateButton?: boolean;
}

function CreateCollectionButton({
  showCreateButton = false,
}: CreateCollectionButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsDialogOpen(true)}>
        {showCreateButton
          ? "Create Your First Collection"
          : "Create Collection"}
      </Button>

      <CollectionDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </>
  );
}

interface CollectionCardWrapperProps {
  collection: CollectionData;
}

function CollectionCardWrapper({ collection }: CollectionCardWrapperProps) {
  const CollectionIcon = getCollectionIcon(collection.icon);
  const colorClasses = getCollectionColorClasses(collection.color);

  return (
    <Link href={`/collections/${collection.id}`} className="block">
      <Card className="hover:shadow-lg transition-shadow cursor-pointer">
        <CardHeader className="flex flex-row items-center space-y-0 pb-2">
          <div className="flex items-center space-x-3 flex-1">
            <div className={cn("p-2 rounded-lg border", colorClasses)}>
              <CollectionIcon
                className={cn("w-5 h-5", colorClasses.split(" ")[0])}
              />
            </div>
            <CardTitle className="text-base md:text-lg">
              {collection.name}
            </CardTitle>
          </div>
          <Badge variant="outline" className="text-xs">
            {collection.isPublic ? "Public" : "Private"}
          </Badge>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-3 text-sm md:text-base">
            {collection.description}
          </p>
          <div className="flex items-center justify-between text-xs md:text-sm text-muted-foreground">
            <span>{collection.tasks.length} tasks</span>
            <span>
              Updated {new Date(collection.updatedAt).toLocaleDateString()}
            </span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
