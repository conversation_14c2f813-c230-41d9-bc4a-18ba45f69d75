import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import PageHeader from "@/components/PageHeader";
import { CollectionActions } from "@/components/collection-actions";
import {
  getCollectionIcon,
  getCollectionColorClasses,
} from "@/lib/collection-utils";
import { cn } from "@/lib/utils";
import { CollectionDetail } from "@/types/models/collection";

interface CollectionDetailPageClientProps {
  collection: CollectionDetail;
  isOwner: boolean;
  solutionLinkPattern?: string;
}

export function CollectionDetailPageClient({
  collection,
  isOwner,
  solutionLinkPattern = "/solutions/{solutionId}",
}: CollectionDetailPageClientProps) {
  const CollectionIcon = getCollectionIcon(collection.icon);
  const colorClasses = getCollectionColorClasses(collection.color);

  return (
    <div className="w-full py-6">
      <PageHeader
        title={
          <div className="flex items-center space-x-4">
            <div className={cn("p-3 rounded-lg border", colorClasses)}>
              <CollectionIcon
                className={cn("w-6 h-6", colorClasses.split(" ")[0])}
              />
            </div>
            <span>{collection.name}</span>
          </div>
        }
        subtitle={collection.description}
        actionsInlineOnMobile={false}
        actions={
          <CollectionActions collection={collection} isOwner={isOwner} />
        }
      />

      {/* Collection Metadata */}
      <div className="mb-8">
        <div className="flex items-center gap-2 sm:gap-4 text-sm text-muted-foreground flex-wrap">
          <span>{collection.tasks.length} tasks</span>
          <span>
            Updated {new Date(collection.updatedAt).toLocaleDateString()}
          </span>
          <Badge variant="outline" className="text-xs">
            {collection.isPublic ? "Public" : "Private"}
          </Badge>
        </div>
      </div>

      {/* Tasks Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Tasks</h2>
        {collection.tasks.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <CollectionIcon className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No tasks yet</h3>
              <p className="text-muted-foreground text-center">
                This collection doesn&apos;t have any tasks yet.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {collection.tasks.map((task: CollectionDetail["tasks"][0]) => (
              <TaskCard
                key={task.id}
                task={task}
                solutionLinkPattern={solutionLinkPattern}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface TaskCardProps {
  task: CollectionDetail["tasks"][0];
  solutionLinkPattern: string;
}

function TaskCard({ task, solutionLinkPattern }: TaskCardProps) {
  const hasPrompt = task.prompts?.promptCraftV4;

  const cardContent = (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <h3 className="font-medium mb-2 text-sm md:text-base leading-tight">
          {task.taskName}
        </h3>
        <p className="text-muted-foreground text-xs md:text-sm leading-relaxed mb-3">
          {task.taskDescription}
        </p>

        {/* Role badges */}
        {task.roles.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {task.roles.map(
              (role: CollectionDetail["tasks"][0]["roles"][0]) => (
                <Badge key={role.id} variant="outline" className="text-xs">
                  {role.roleName}
                </Badge>
              )
            )}
          </div>
        )}

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{hasPrompt ? "Solution available" : "No solution yet"}</span>
          <span>{new Date(task.updatedAt).toLocaleDateString()}</span>
        </div>
      </CardContent>
    </Card>
  );

  // If task has a solution, make it clickable
  if (hasPrompt) {
    const solutionLink = solutionLinkPattern.replace(
      "{solutionId}",
      task.prompts.promptCraftV4!
    );
    return <Link href={solutionLink}>{cardContent}</Link>;
  }

  return cardContent;
}
