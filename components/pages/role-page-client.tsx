import Link from "next/link";
import { RoleItem } from "@/types/models/role";
import { TaskItem } from "@/types/models/task";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface RolePageClientProps {
  role: RoleItem;
  tasks: TaskItem[];
  solutionLinkPattern?: string;
}

export function RolePageClient({
  role,
  tasks,
  solutionLinkPattern = "/solutions/{solutionId}",
}: RolePageClientProps) {
  return (
    <div className="w-full py-6 sm:py-8">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2">{role.roleName}</h1>
        <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">
          {role.roleDescription}
        </p>
      </div>

      <div className="mb-6">
        <h2 className="text-xl sm:text-2xl font-semibold mb-4">Tasks</h2>
        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {tasks
            .filter((task) => task.prompts.promptCraftV4) // Only show tasks with solutions
            .map((task) => {
              const solutionLink = solutionLinkPattern.replace(
                "{solutionId}",
                task.prompts.promptCraftV4!
              );
              return (
                <Link
                  key={task.id}
                  href={solutionLink}
                  className="block w-full"
                >
                  <Card className="h-full hover:shadow-md transition-shadow cursor-pointer w-full">
                    <CardHeader className="pb-3 sm:pb-6">
                      <CardTitle className="text-lg sm:text-xl leading-tight">
                        {task.taskName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-sm leading-relaxed">
                        {task.taskDescription}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
        </div>
      </div>
    </div>
  );
}
