import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import Collection from "@/models/Collection";
import connectMongo from "@/libs/mongoose";
import {
  CollectionData,
  collectionMongooseParser,
} from "@/types/models/collection";
import { CollectionsPageClient } from "./collections-page-client";
import config from "@/config";

export const dynamic = "force-dynamic";

export async function CollectionsPage() {
  // Get session and redirect if not authenticated
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect(config.auth.loginUrl);
  }

  const user = session.user;

  await connectMongo();

  // Show user's own collections (both public and private)
  const userCollections = await Collection.find({
    userId: user.id,
  })
    .sort({ updatedAt: -1 })
    .lean();

  const collections: CollectionData[] = userCollections.map(
    collectionMongooseParser
  );

  return <CollectionsPageClient collections={collections} />;
}
