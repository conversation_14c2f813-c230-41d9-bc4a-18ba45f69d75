import { SolutionItem } from "@/types/models/solution";
import { SolutionPageClient } from "./solution-page-client";
import Task from "@/models/Task";
import connectMongo from "@/libs/mongoose";
import { getUserCollectionsServer } from "@/lib/server-collection-utils";

export const revalidate = 300;

interface SolutionPageProps {
  solution: SolutionItem;
}

export async function SolutionPage({ solution }: SolutionPageProps) {
  // Get associated task and collections
  await connectMongo();
  const task = await Task.findOne({
    "prompts.promptCraftV4": solution.id,
  });

  // Fetch user's collections using the utility function
  const collections = await getUserCollectionsServer();

  const taskId = task?._id?.toString() || null;

  return (
    <SolutionPageClient
      solution={solution}
      taskId={taskId}
      collections={collections}
    />
  );
}
