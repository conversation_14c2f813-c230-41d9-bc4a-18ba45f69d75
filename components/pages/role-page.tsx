import { getRoleBySlug, getTasksByRoleSlug } from "@/lib/database";
import { notFound } from "next/navigation";
import { RoleItem } from "@/types/models/role";
import { TaskItem } from "@/types/models/task";
import { RolePageClient } from "./role-page-client";

export const revalidate = 300;

interface RolePageProps {
  roleSlug: string;
  solutionLinkPattern?: string;
}

export async function RolePage({
  roleSlug,
  solutionLinkPattern = "/solutions/{solutionId}",
}: RolePageProps) {
  // Get data from database
  const [role, roleTasks] = await Promise.all([
    getRoleBySlug({ slug: roleSlug }),
    getTasksByRoleSlug({ roleSlug }),
  ]);

  if (!role) {
    notFound();
  }

  // Convert Mongoose documents to plain objects
  const plainRole: RoleItem = JSON.parse(JSON.stringify(role));
  const plainRoleTasks: TaskItem[] = JSON.parse(JSON.stringify(roleTasks));

  return (
    <RolePageClient
      role={plainRole}
      tasks={plainRoleTasks}
      solutionLinkPattern={solutionLinkPattern}
    />
  );
}
