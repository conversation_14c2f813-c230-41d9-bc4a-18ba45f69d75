"use client";

import { SolutionPageView } from "@/components/solution-page-view";
import { SolutionItem } from "@/types/models/solution";
import { useTrackSolutionView } from "@/hooks/use-track-solution-view";
import { CollectionItem } from "@/types/models/collection";

interface SolutionPageClientProps {
  solution: SolutionItem;
  taskId: string | null;
  collections: CollectionItem[] | null;
}

export function SolutionPageClient({
  solution,
  taskId,
  collections,
}: SolutionPageClientProps) {
  // Track solution view
  useTrackSolutionView(solution.id);

  return (
    <SolutionPageView
      solution={solution}
      taskId={taskId}
      collections={collections}
    />
  );
}
