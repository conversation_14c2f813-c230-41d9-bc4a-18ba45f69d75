import { notFound } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { CollectionDetailPageClient } from "./collection-detail-page-client";
import { CollectionDetail } from "@/types/models/collection";

export const dynamic = "force-dynamic";

interface CollectionDetailPageProps {
  collection: CollectionDetail;
  solutionLinkPattern?: string;
}

export async function CollectionDetailPage({
  collection,
  solutionLinkPattern = "/solutions/{solutionId}",
}: CollectionDetailPageProps) {
  // Get session without requiring authentication
  const session = await getServerSession(authOptions);
  const user = session?.user;

  // Check if user can view this collection
  const isOwner = user && collection.userId.toString() === user.id;
  const canView = collection.isPublic || isOwner;

  if (!canView) {
    notFound();
  }

  return (
    <CollectionDetailPageClient
      collection={collection}
      isOwner={isOwner || false}
      solutionLinkPattern={solutionLinkPattern}
    />
  );
}
