"use client";
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { SidebarMenuButton, SidebarMenuItem } from "./ui/sidebar";
import { cn } from "@/lib/utils";

type SidebarMenuNavigationButtonProps = {
  title: string;
  url: string;
  children: React.ReactNode;
  badge?: number;
  className?: string;
};

export function SidebarMenuNavigationButton({
  title,
  url,
  children,
  badge,
  className,
}: SidebarMenuNavigationButtonProps) {
  const pathname = usePathname();
  const isActive = pathname === url;
  return (
    <SidebarMenuItem key={title}>
      <SidebarMenuButton asChild>
        <Link
          href={url}
          className={cn(
            "flex items-center gap-3 justify-between",
            isActive
              ? "font-medium text-sidebar-primary"
              : "text-sidebar-foreground",
            className
          )}
          data-active={isActive}
        >
          <div className="flex items-center gap-3">
            {children}
            <span>{title}</span>
          </div>
          {badge !== undefined && (
            <span className="flex items-center justify-center min-w-5 h-5 rounded-full bg-sidebar-primary text-sidebar-primary-foreground text-xs font-medium px-1.5">
              {badge}
            </span>
          )}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
