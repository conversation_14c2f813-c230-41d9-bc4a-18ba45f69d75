"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronDown, Zap, Edit, Check, Copy } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { INTERACTIVE_PROMPT_PREFIX } from "@/lib/constants";
import { useAuthGuard } from "@/hooks/use-auth-guard";

interface CopyButtonProps {
  text: string;
  className?: string;
  onFillAndCopy?: () => void;
  onCopyClick?: () => void;
}

const COPY_STORAGE_KEY = "preferred-copy-option";
const COPY_PREFERENCE_EVENT = "copyPreferenceChanged";

export function CopyButton({
  text,
  className,
  onFillAndCopy,
  onCopyClick,
}: CopyButtonProps) {
  const [copied, setCopied] = useState<string | null>(null);
  const [preferredCopyOption, setPreferredCopyOption] =
    useState<string>("prompt");

  const { executeWithAuth, SignInDialog } = useAuthGuard({
    title: "Sign in to copy prompts",
    description:
      "Create a free account to copy prompts and access all features",
  });

  // Load saved preference from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(COPY_STORAGE_KEY);
      if (saved && ["prompt", "interactive", "fill"].includes(saved)) {
        setPreferredCopyOption(saved);
      }
    } catch (error) {
      console.warn("Failed to load copy preference:", error);
    }
  }, []);

  // Listen for preference changes from other copy buttons
  useEffect(() => {
    const handlePreferenceChange = (event: CustomEvent) => {
      const newPreference = event.detail.preference;
      if (["prompt", "interactive", "fill"].includes(newPreference)) {
        setPreferredCopyOption(newPreference);
      }
    };

    window.addEventListener(
      COPY_PREFERENCE_EVENT,
      handlePreferenceChange as EventListener
    );

    return () => {
      window.removeEventListener(
        COPY_PREFERENCE_EVENT,
        handlePreferenceChange as EventListener
      );
    };
  }, []);

  const updatePreference = (newPreference: string) => {
    if (["prompt", "interactive", "fill"].includes(newPreference)) {
      try {
        localStorage.setItem(COPY_STORAGE_KEY, newPreference);
        setPreferredCopyOption(newPreference);

        // Notify other copy buttons on the page
        const event = new CustomEvent(COPY_PREFERENCE_EVENT, {
          detail: { preference: newPreference },
        });
        window.dispatchEvent(event);
      } catch (error) {
        console.warn("Failed to save copy preference:", error);
      }
    }
  };

  const handleCopy = async (copyText: string, type: string) => {
    try {
      await navigator.clipboard.writeText(copyText);
      setCopied(type);

      // Update preference for prompt/interactive copies
      if (["prompt", "interactive"].includes(type)) {
        updatePreference(type);
      }

      setTimeout(() => setCopied(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const copyPrompt = () => {
    executeWithAuth(() => {
      onCopyClick?.();
      handleCopy(text, "prompt");
    });
  };

  const copyInteractivePrompt = () => {
    executeWithAuth(() => {
      onCopyClick?.();
      const interactivePrompt = INTERACTIVE_PROMPT_PREFIX + text;
      handleCopy(interactivePrompt, "interactive");
    });
  };

  const handleFillAndCopy = () => {
    executeWithAuth(() => {
      // Save "fill" as the preferred action
      updatePreference("fill");

      if (onFillAndCopy) {
        onFillAndCopy();
      }
    });
  };

  // Primary action based on preference
  const handlePrimaryAction = () => {
    if (preferredCopyOption === "interactive") {
      copyInteractivePrompt();
    } else if (preferredCopyOption === "fill" && onFillAndCopy) {
      handleFillAndCopy();
    } else {
      copyPrompt();
    }
  };

  // Get the appropriate icon based on preferred copy option
  const getPreferredIcon = () => {
    const iconSize = isCompact ? "h-3 w-3" : "h-4 w-4";

    if (preferredCopyOption === "interactive") {
      return <Zap className={`shrink-0 ${iconSize}`} />;
    } else if (preferredCopyOption === "fill" && onFillAndCopy) {
      return <Edit className={`shrink-0 ${iconSize}`} />;
    } else {
      return <Copy className={`shrink-0 ${iconSize}`} />;
    }
  };

  // Get the appropriate text based on preferred copy option
  const getPreferredText = () => {
    if (preferredCopyOption === "interactive") {
      return "Copy Interactive";
    } else if (preferredCopyOption === "fill" && onFillAndCopy) {
      return "Fill & Copy";
    } else {
      return "Copy Prompt";
    }
  };

  // Determine if this is a compact/modal context based on className
  const isCompact =
    className?.includes("h-7") || className?.includes("text-xs");

  // Create base button classes
  const baseButtonClasses = isCompact
    ? "h-7 px-2 text-xs gap-1"
    : "h-8 px-3 text-sm gap-1.5";

  return (
    <>
      <div className={`flex max-w-full ${isCompact ? "text-xs" : ""}`}>
        {/* Primary copy button - always shows "Copy" */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePrimaryAction}
          className={`rounded-r-none border-r border-border text-muted-foreground hover:text-foreground hover:bg-muted flex-1 min-w-0 ${baseButtonClasses} ${className}`}
          aria-label="Copy prompt to clipboard"
        >
          {copied === preferredCopyOption ? (
            <>
              <Check
                className={`shrink-0 ${
                  isCompact ? "h-3 w-3" : "h-4 w-4"
                } text-green-600`}
              />
              <span className="truncate text-green-600">
                {isCompact ? "Copied" : "Copied"}
              </span>
            </>
          ) : (
            <>
              {getPreferredIcon()}
              <span className="truncate">
                {isCompact ? "Copy" : getPreferredText()}
              </span>
            </>
          )}
        </Button>

        {/* Dropdown for copy options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`rounded-l-none text-muted-foreground hover:text-foreground hover:bg-muted border-l-0 shrink-0 ${baseButtonClasses} px-1.5`}
              aria-label="Copy options"
            >
              <ChevronDown
                className={`${isCompact ? "h-3 w-3" : "h-3.5 w-3.5"}`}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Copy Options
            </div>
            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={copyPrompt}
              className="cursor-pointer flex items-center justify-between py-2"
            >
              <div className="flex items-center">
                <Copy className="mr-2.5 h-4 w-4" />
                <span className="font-medium">Copy Prompt</span>
              </div>
              <div className="flex items-center gap-2">
                {copied === "prompt" && (
                  <span className="text-xs text-green-600 font-medium">
                    Copied
                  </span>
                )}
                {preferredCopyOption === "prompt" && (
                  <Check className="h-4 w-4 text-blue-600" />
                )}
              </div>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={copyInteractivePrompt}
              className="cursor-pointer flex items-center justify-between py-2"
            >
              <div className="flex items-center">
                <Zap className="mr-2.5 h-4 w-4" />
                <span className="font-medium">Copy Interactive Prompt</span>
              </div>
              <div className="flex items-center gap-2">
                {copied === "interactive" && (
                  <span className="text-xs text-green-600 font-medium">
                    Copied
                  </span>
                )}
                {preferredCopyOption === "interactive" && (
                  <Check className="h-4 w-4 text-blue-600" />
                )}
              </div>
            </DropdownMenuItem>

            {onFillAndCopy && (
              <DropdownMenuItem
                onClick={handleFillAndCopy}
                className="cursor-pointer flex items-center justify-between py-2"
              >
                <div className="flex items-center">
                  <Edit className="mr-2.5 h-4 w-4" />
                  <span className="font-medium">Fill & Copy</span>
                </div>
                {preferredCopyOption === "fill" && (
                  <Check className="h-4 w-4 text-blue-600" />
                )}
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              Your preferred option is saved automatically
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Sign In Dialog */}
      <SignInDialog />
    </>
  );
}
