"use client";

import { useState, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { HomeSearchWithFilters } from "@/components/home-search-with-filters";
import { HomeResultsSection } from "@/components/home-results-section";
import AppHeader from "@/components/app-header";
import {
  UnifiedSearchResult,
  UnifiedSearchResponse,
} from "@/types/models/search";
import type { RoleItem } from "@/types/models/role";
import type { CollectionItem } from "@/types/models/collection";

interface HomePageHybridProps {
  initialRoles: RoleItem[];
  initialTasks: UnifiedSearchResponse;
  collections: CollectionItem[];
}

export function HomePageHybrid({
  initialRoles,
  initialTasks,
  collections,
}: HomePageHybridProps) {
  const [searchResults, setSearchResults] = useState<
    UnifiedSearchResult[] | null
  >(initialTasks.results);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchTotalCount, setSearchTotalCount] = useState(
    initialTasks.totalCount
  );
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);

  const handleResults = useCallback(
    (
      results: UnifiedSearchResult[] | null,
      searching: boolean,
      query: string,
      totalCount: number,
      fetchingNextPage: boolean
    ) => {
      setSearchResults(results);
      setIsSearching(searching);
      setSearchQuery(query);
      setSearchTotalCount(totalCount);
      setIsFetchingNextPage(fetchingNextPage);
    },
    []
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <AppHeader collections={collections} />

      {/* Hero Section */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-8 pb-6">
        <div className="text-center max-w-4xl mx-auto">
          {/* Beta Launch Badge */}
          <div className="mb-3">
            <Badge variant="info" className="text-sm px-4 py-1">
              Beta Launch
            </Badge>
          </div>

          {/* Main Title */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4">
            Find AI Solutions
            <br />
            <span className="text-primary">for Any Task</span>
          </h1>

          {/* Subtitle */}
          <p className="text-md sm:text-xl text-muted-foreground mb-6 max-w-3xl mx-auto leading-relaxed">
            Simply input the task you want to achieve, and we&apos;ll handle the
            rest - <br className="hidden sm:block" />
            no searching, no tech jargon, just results.
          </p>

          {/* Combined Search and Filter Component */}
          <HomeSearchWithFilters
            onResults={handleResults}
            initialRoles={initialRoles}
            initialTasks={initialTasks}
          />
        </div>
      </div>

      {/* Results Section */}
      <HomeResultsSection
        searchResults={searchResults}
        isSearching={isSearching}
        searchQuery={searchQuery}
        searchTotalCount={searchTotalCount}
        isFetchingNextPage={isFetchingNextPage}
      />
    </div>
  );
}
