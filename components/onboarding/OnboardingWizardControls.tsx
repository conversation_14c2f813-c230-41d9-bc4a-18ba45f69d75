"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { trpc } from "@/utils/trpc/client";
import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { OnboardingStepId } from "@/types/models/user";

interface OnboardingWizardControlsProps {
  nextStepId?: OnboardingStepId;
  onStepComplete?: () => Promise<boolean>;
  disableNextButton?: boolean;
}

export function OnboardingWizardControls({
  nextStepId,
  onStepComplete,
  disableNextButton,
}: OnboardingWizardControlsProps) {
  const router = useRouter();
  const isLastStep = nextStepId == null;

  // TRPC mutations
  const updateStepMutation = trpc.onboarding.updateStep.useMutation();

  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  const handleNextStep = useCallback(async () => {
    // First, run the step-specific completion logic
    if (onStepComplete) {
      const success = await onStepComplete();
      if (!success) return;
    }

    // Then update the onboarding step in the database
    try {
      await updateStepMutation.mutateAsync({
        completed: isLastStep,
        step: nextStepId || "finished",
      });
      router.refresh();
      scrollToTop();
    } catch (error) {
      console.error("Failed to update onboarding step:", error);
    }
  }, [
    nextStepId,
    isLastStep,
    onStepComplete,
    updateStepMutation,
    router,
    scrollToTop,
  ]);

  return (
    <div className="flex justify-center sm:justify-end my-6">
      <Button
        onClick={handleNextStep}
        disabled={updateStepMutation.isLoading || disableNextButton}
        className="w-full sm:w-auto"
        size="lg"
      >
        {updateStepMutation.isLoading
          ? "Processing..."
          : isLastStep
            ? "Complete"
            : "Next"}
      </Button>
    </div>
  );
}
