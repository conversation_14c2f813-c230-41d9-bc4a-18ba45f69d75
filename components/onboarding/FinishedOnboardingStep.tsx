import { ONBOARDING_CONFIG } from "./onboarding-config";
import OnboardingFooter from "./OnboardingFooter";
import { OnboardingWizardControls } from "./OnboardingWizardControls";

export default function FinishedOnboardingStep() {
  return (
    <div>
      <div>Finished!</div>
      <OnboardingWizardControls />
      <OnboardingFooter
        totalSteps={ONBOARDING_CONFIG.totalSteps}
        totalStepsCompleted={ONBOARDING_CONFIG.totalSteps - 1}
      />
    </div>
  );
}
