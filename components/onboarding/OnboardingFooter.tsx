type OnboardingFooterProps = {
  totalSteps: number;
  totalStepsCompleted: number;
};
export default function OnboardingFooter({
  totalSteps,
  totalStepsCompleted,
}: OnboardingFooterProps) {
  return (
    <div className="border-t p-4">
      <div className="flex justify-center gap-2">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <div
            key={index}
            className={`h-2 w-16 rounded-full ${
              index === totalStepsCompleted ? "bg-primary" : "bg-muted"
            }`}
          />
        ))}
      </div>
    </div>
  );
}
