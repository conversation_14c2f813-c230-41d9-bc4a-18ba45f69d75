import { IUser } from "@/models/User";
import WelcomeOnboardingStep from "./WelcomeOnboardingStep";
import FinishedOnboardingStep from "./FinishedOnboardingStep";
import { userMongooseParser } from "@/types/models/user";

type OnboardingWizardProps = {
  user: IUser;
};

type CurrentStepInfo = {
  component: React.ReactNode;
  title: string;
  description: string;
};
const getCurrentStep = (user: IUser): CurrentStepInfo => {
  const userItem = userMongooseParser.parse(user);
  if (userItem.onboarding.step === "welcome") {
    return {
      component: <WelcomeOnboardingStep user={userItem} />,
      title: "Welcome!",
      description: "Welcome to the onboarding wizard!",
    };
  } else if (userItem.onboarding.step === "finished") {
    return {
      component: <FinishedOnboardingStep />,
      title: "Finished!",
      description: "You have finished the onboarding wizard!",
    };
  }
  throw new Error("Invalid onboarding step");
};

export default function OnboardingWizard({ user }: OnboardingWizardProps) {
  // Get current step info with fallback
  const currentStepInfo = getCurrentStep(user);

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <header className="border-b p-6">
        <h1 className="text-2xl font-bold">{currentStepInfo.title}</h1>
        <p className="text-muted-foreground">{currentStepInfo.description}</p>
      </header>

      <main className="flex-1 p-4 md:p-6 overflow-auto">
        {currentStepInfo.component}
      </main>
    </div>
  );
}
