"use client";
import { UserItem } from "@/types/models/user";
import { ONBOARDING_CONFIG } from "./onboarding-config";
import OnboardingFooter from "./OnboardingFooter";
import { OnboardingWizardControls } from "./OnboardingWizardControls";

type WelcomeOnboardingStepProps = {
  user: UserItem;
};

export default function WelcomeOnboardingStep({
  user,
}: WelcomeOnboardingStepProps) {
  return (
    <div>
      <div>{user.name ? `Welcome ${user.name}!` : "Welcome!"}</div>
      <OnboardingWizardControls nextStepId="finished" />
      <OnboardingFooter
        totalSteps={ONBOARDING_CONFIG.totalSteps}
        totalStepsCompleted={0}
      />
    </div>
  );
}
