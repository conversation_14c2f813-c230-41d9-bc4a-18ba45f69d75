"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { Search, X } from "lucide-react";
import { trpc } from "@/utils/trpc/client";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import {
  UnifiedSearchResult,
  UnifiedSearchResponse,
} from "@/types/models/search";
import type { RoleItem } from "@/types/models/role";

interface HomeSearchWithFiltersProps {
  onResults: (
    results: UnifiedSearchResult[] | null,
    isSearching: boolean,
    query: string,
    totalCount: number,
    isFetchingNextPage: boolean
  ) => void;
  initialRoles?: RoleItem[];
  initialTasks?: UnifiedSearchResponse;
}

export function HomeSearchWithFilters({
  onResults,
  initialRoles,
  initialTasks,
}: HomeSearchWithFiltersProps) {
  const [query, setQuery] = useState("");
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [isActive, setIsActive] = useState(false);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // Debounce the search query to avoid too many API calls
  const debouncedQuery = useDebounce(query, 500);

  // Get roles for filtering
  const {
    data: roles,
    isLoading: rolesLoading,
    error: rolesError,
  } = trpc.content.getRoles.useQuery(undefined, {
    initialData: initialRoles,
  });

  // Use infinite query for unified search
  const {
    data: searchData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isSearching,
    error: searchError,
    refetch: refetchSearch,
  } = trpc.search.searchTasks.useInfiniteQuery(
    {
      query: debouncedQuery,
      roleFilter: selectedRoles.length > 0 ? selectedRoles : undefined,
      limit: 20,
      minScore: 0.3,
    },
    {
      enabled: true, // Always enabled since empty query means browse all
      staleTime: 1000 * 60 * 5, // Cache for 5 minutes
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      initialData:
        initialTasks && debouncedQuery === "" && !hasUserInteracted
          ? {
              pages: [initialTasks],
              pageParams: [undefined],
            }
          : undefined,
    }
  );

  // Flatten search results from all pages - memoized to prevent useEffect dependency changes
  const searchResults = useMemo(() => {
    return searchData?.pages.flatMap((page) => page.results) ?? [];
  }, [searchData]);

  // Get total results count
  const searchTotalCount = searchData?.pages[0]?.totalCount ?? 0;

  // Infinite scroll handler
  const handleScroll = useCallback(() => {
    if (!isSearching && !isFetchingNextPage && hasNextPage) {
      const scrollPosition = window.scrollY + window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Trigger fetch when user is within 200px of bottom
      if (scrollPosition >= documentHeight - 200) {
        fetchNextPage();
      }
    }
  }, [isSearching, isFetchingNextPage, hasNextPage, fetchNextPage]);

  // Set up infinite scroll
  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  // Effect to handle search results
  useEffect(() => {
    const isActuallyLoading = isSearching || isFilterLoading;

    if (searchResults.length > 0) {
      onResults(
        searchResults,
        false,
        debouncedQuery,
        searchTotalCount,
        isFetchingNextPage
      );
      setIsActive(true);
      setIsFilterLoading(false); // Clear filter loading when results arrive
    } else if (isActuallyLoading) {
      onResults([], true, debouncedQuery, searchTotalCount, isFetchingNextPage);
      setIsActive(true);
    } else if (searchData?.pages.length && searchResults.length === 0) {
      onResults(
        [],
        false,
        debouncedQuery,
        searchTotalCount,
        isFetchingNextPage
      );
      setIsActive(true);
      setIsFilterLoading(false); // Clear filter loading for empty results
    } else if (debouncedQuery.trim().length === 0 && !isActuallyLoading) {
      // For empty query, still show results but indicate browsing mode
      onResults(
        searchResults.length > 0 ? searchResults : null,
        false,
        "",
        searchTotalCount,
        isFetchingNextPage
      );
      setIsActive(false);
      setIsFilterLoading(false); // Clear filter loading for browse mode
    }
  }, [
    debouncedQuery,
    searchResults,
    isSearching,
    isFilterLoading,
    searchData?.pages.length,
    searchTotalCount,
    isFetchingNextPage,
    onResults,
  ]);

  // Refetch search when role filters change
  useEffect(() => {
    if (selectedRoles.length > 0 || searchData?.pages.length) {
      setIsFilterLoading(true);
      refetchSearch();
    }
  }, [selectedRoles, refetchSearch, searchData?.pages.length]);

  const handleClearSearch = () => {
    setQuery("");
    setIsActive(false);
    setHasUserInteracted(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setHasUserInteracted(true);
  };

  const handleRoleToggle = useCallback((roleId: string) => {
    setHasUserInteracted(true);
    setSelectedRoles((prev) => {
      if (prev.includes(roleId)) {
        return prev.filter((id) => id !== roleId);
      } else {
        return [...prev, roleId];
      }
    });
  }, []);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative max-w-2xl mx-auto">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
        <Input
          value={query}
          onChange={handleInputChange}
          placeholder="What task are you working on?"
          className="pl-12 pr-12 py-6 text-sm sm:text-lg border-2 border-input focus:border-primary transition-colors"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Role Filter Badges */}
      <div className="flex flex-wrap justify-center gap-2 sm:gap-3">
        {rolesError
          ? null
          : rolesLoading
            ? Array.from({ length: 12 }).map((_, i) => (
                <Skeleton key={i} className="h-8 w-20" />
              ))
            : roles?.map((role) => {
                const isSelected = selectedRoles.includes(role.id);
                return (
                  <Badge
                    key={role.id}
                    variant={isSelected ? "default" : "secondary"}
                    className={`hover:bg-primary hover:text-primary-foreground transition-all cursor-pointer px-4 py-2 text-sm ${
                      isSelected
                        ? "ring-2 ring-primary/20 bg-primary text-primary-foreground"
                        : ""
                    }`}
                    onClick={() => handleRoleToggle(role.id)}
                  >
                    {role.roleName}
                  </Badge>
                );
              })}
      </div>

      {/* Search Status */}
      {isActive && (
        <div className="text-center">
          {(isSearching || isFilterLoading) && searchResults.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              {debouncedQuery.trim().length > 0
                ? `Searching for "${debouncedQuery}"...`
                : selectedRoles.length > 0
                  ? "Filtering tasks..."
                  : "Loading tasks..."}
            </p>
          ) : searchError ? (
            <p className="text-sm text-destructive">
              Search failed. Please try again.
            </p>
          ) : searchData?.pages.length ? (
            <div className="space-y-2">
              {selectedRoles.length > 0 && (
                <div className="flex items-center justify-center gap-2 flex-wrap">
                  <span className="text-xs text-muted-foreground">
                    Filtered by:
                  </span>
                  {selectedRoles.map((roleId) => {
                    const role = roles?.find((r) => r.id === roleId);
                    return (
                      <Badge
                        key={roleId}
                        variant="secondary"
                        className="text-xs cursor-pointer"
                        onClick={() => handleRoleToggle(roleId)}
                      >
                        {role?.roleName || `Role ${roleId}`}
                        <X className="ml-1 h-3 w-3" />
                      </Badge>
                    );
                  })}
                </div>
              )}
              {isFetchingNextPage && (
                <p className="text-xs text-muted-foreground">
                  Loading more results...
                </p>
              )}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
