"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useSearchParams, useRouter } from "next/navigation";
import { getProviders } from "next-auth/react";
import { SignInForm } from "@/components/auth/signin-form";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import config from "@/config";

type Providers = Awaited<ReturnType<typeof getProviders>>;

export function SignInPageContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [providers, setProviders] = useState<Providers>(null);

  useEffect(() => {
    // If user is already logged in, redirect to callback URL or home
    if (session) {
      const callbackUrl =
        searchParams.get("callbackUrl") || config.auth.callbackUrl;
      router.push(callbackUrl);
    }
  }, [session, router, searchParams]);

  useEffect(() => {
    getProviders().then(setProviders);
  }, []);

  const error = searchParams.get("error");
  const callbackUrl =
    searchParams.get("callbackUrl") || config.auth.callbackUrl;

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-card rounded-lg shadow-lg border border-border p-8">
          {/* Back Button */}
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center gap-2 px-0 text-muted-foreground hover:text-foreground p-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
          </div>
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Image
                src="/logoTransparent.png"
                alt={config.metadata.appName}
                className="h-12 w-auto"
                width={200}
                height={48}
                priority
              />
              <span className="font-bold text-base sm:text-lg">
                {config.metadata.appName}
              </span>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              Welcome back
            </h1>
            <p className="text-muted-foreground text-sm">
              Sign in to continue to {config.metadata.appName}
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-destructive text-sm text-center">
                {error === "OAuthCallback"
                  ? "There was an error signing in. Please try again."
                  : "Authentication failed. Please try again."}
              </p>
            </div>
          )}

          {/* Sign In Form */}
          <SignInForm providers={providers} callbackUrl={callbackUrl} />

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-muted-foreground">
              By signing in, you agree to our{" "}
              <a href="/tos" className="text-primary hover:underline">
                Terms of Service
              </a>{" "}
              and{" "}
              <a
                href="/privacy-policy"
                className="text-primary hover:underline"
              >
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
