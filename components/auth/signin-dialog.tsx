"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { getProviders } from "next-auth/react";
import { SignInForm } from "@/components/auth/signin-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Star } from "lucide-react";
import Image from "next/image";
import config from "@/config";

type Providers = Awaited<ReturnType<typeof getProviders>>;

interface SignInDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  title?: string;
  description?: string;
}

export function SignInDialog({
  open,
  onOpenChange,
  onSuccess,
  title = "Sign in to continue",
  description = "Access this feature by signing in to your account",
}: SignInDialogProps) {
  const { data: session } = useSession();
  const [providers, setProviders] = useState<Providers>(null);

  // Get the current page URL as callback URL
  const callbackUrl =
    typeof window !== "undefined"
      ? window.location.href
      : config.auth.callbackUrl;

  useEffect(() => {
    // If user is already logged in, close dialog and call onSuccess
    if (session) {
      onOpenChange(false);
      onSuccess?.();
    }
  }, [session, onOpenChange, onSuccess]);

  useEffect(() => {
    if (open) {
      getProviders().then(setProviders);
    }
  }, [open]);

  const benefits = [
    {
      icon: <CheckCircle className="w-4 h-4 text-green-500" />,
      text: "100% free signup",
    },
    {
      icon: <Star className="w-4 h-4 text-yellow-500" />,
      text: "No credit card required",
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center space-y-4">
          {/* Logo */}
          <div className="flex items-center justify-center mb-2">
            <Image
              src="/logoTransparent.png"
              alt={config.metadata.appName}
              className="h-8 w-auto"
              width={160}
              height={32}
              priority
            />
            <span className="font-bold text-lg ml-2">
              {config.metadata.appName}
            </span>
          </div>

          <DialogTitle className="text-xl">{title}</DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            {description}
          </DialogDescription>

          {/* Benefits */}
          <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Badge
                variant="secondary"
                className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100"
              >
                FREE ACCOUNT
              </Badge>
            </div>
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-3 text-sm">
                {benefit.icon}
                <span className="font-medium">{benefit.text}</span>
              </div>
            ))}
          </div>
        </DialogHeader>

        <div className="mt-6">
          <SignInForm
            providers={providers}
            callbackUrl={callbackUrl}
            isInDialog={true}
          />
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            By signing in, you agree to our{" "}
            <a
              href="/tos"
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="/privacy-policy"
              className="text-primary hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
