"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { signIn, getProviders } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Mail, Chrome, Loader2 } from "lucide-react";

interface SignInFormData {
  email: string;
}

type Providers = Awaited<ReturnType<typeof getProviders>>;

interface SignInFormProps {
  providers: Providers;
  callbackUrl: string;
  isInDialog?: boolean;
}

export function SignInForm({
  providers,
  callbackUrl,
  isInDialog: _isInDialog = false,
}: SignInFormProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<SignInFormData>();

  const handleOAuthSignIn = async (providerId: string) => {
    setIsLoading(providerId);
    try {
      await signIn(providerId, { callbackUrl });
    } catch (error) {
      console.error("Sign in error:", error);
    } finally {
      setIsLoading(null);
    }
  };

  const handleEmailSignIn = async (data: SignInFormData) => {
    setIsLoading("email");
    try {
      const result = await signIn("email", {
        email: data.email,
        callbackUrl,
        redirect: false,
      });

      if (result?.ok) {
        setEmailSent(true);
      }
    } catch (error) {
      console.error("Email sign in error:", error);
    } finally {
      setIsLoading(null);
    }
  };

  // Show success message if email was sent
  if (emailSent) {
    return (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
          <Mail className="w-8 h-8 text-primary" />
        </div>
        <div>
          <h2 className="text-lg font-semibold text-foreground mb-2">
            Check your email
          </h2>
          <p className="text-sm text-muted-foreground">
            We&apos;ve sent a sign-in link to your email address. Please check
            your inbox and click the link to complete your sign-in.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Google Sign In */}
      {providers?.google && (
        <div>
          <Button
            onClick={() => handleOAuthSignIn("google")}
            disabled={isLoading !== null}
            className="w-full h-11 bg-background border border-border hover:bg-accent text-foreground shadow-sm"
            variant="outline"
          >
            {isLoading === "google" ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Chrome className="w-4 h-4 mr-2" />
            )}
            Continue with Google
          </Button>
        </div>
      )}

      {/* Divider */}
      {providers?.google && providers?.email && (
        <div className="relative">
          <Separator />
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="bg-card px-3 text-xs text-muted-foreground">
              Or continue with
            </span>
          </div>
        </div>
      )}

      {/* Email Sign In */}
      {providers?.email && (
        <form onSubmit={handleSubmit(handleEmailSignIn)} className="space-y-4">
          <div>
            <Input
              type="email"
              placeholder="Enter your email"
              className="h-11"
              disabled={isLoading !== null}
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Please enter a valid email address",
                },
              })}
            />
            {errors.email && (
              <p className="text-sm text-destructive mt-1">
                {errors.email.message}
              </p>
            )}
          </div>
          <Button
            type="submit"
            disabled={isLoading !== null || isSubmitting}
            className="w-full h-11"
          >
            {isLoading === "email" ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Mail className="w-4 h-4 mr-2" />
            )}
            Send sign-in link
          </Button>
        </form>
      )}

      {/* No providers message */}
      {!providers?.google && !providers?.email && (
        <div className="text-center text-muted-foreground">
          <p>No sign-in providers are currently available.</p>
        </div>
      )}
    </div>
  );
}
