/* eslint-disable @next/next/no-img-element */
"use client";

import { useSession, signOut, signIn } from "next-auth/react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { User, LogOut, CreditCard, BookmarkIcon, Settings } from "lucide-react";
import { trpc } from "@/utils/trpc/client";
import config from "@/config";
import Link from "next/link";

const RENDER_BILLING = false;
const RENDER_DASHBOARD = false;

export default function AuthButton() {
  const { data: session, status } = useSession();
  // Use the tRPC mutation for billing portal
  const createPortalMutation = trpc.stripe.createPortal.useMutation({
    onSuccess: (data) => {
      window.location.href = data.url;
    },
    onError: (error) => {
      console.error("Failed to create customer portal:", error);
    },
  });

  const handleSignIn = () => {
    // If on home page, redirect to default callback URL
    // Otherwise, redirect back to current page
    const callbackUrl =
      window.location.pathname === "/"
        ? config.auth.callbackUrl
        : window.location.href;

    signIn(undefined, { callbackUrl });
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" });
  };

  const handleBilling = () => {
    createPortalMutation.mutate({
      returnUrl: window.location.href,
    });
  };

  // Show loading state
  if (status === "loading") {
    return (
      <Button variant="outline" disabled>
        Loading...
      </Button>
    );
  }

  // Show login button when not authenticated
  if (status === "unauthenticated") {
    return (
      <Button onClick={handleSignIn} variant="outline">
        Sign In
      </Button>
    );
  }

  // Show user dropdown when authenticated
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Avatar className="w-6 h-6">
            {session?.user?.image ? (
              <AvatarImage
                src={session.user.image}
                alt={session.user.name || "User"}
                referrerPolicy="no-referrer"
              />
            ) : (
              <AvatarFallback className="text-xs">
                {session?.user?.name?.charAt(0) ||
                  session?.user?.email?.charAt(0) ||
                  "U"}
              </AvatarFallback>
            )}
          </Avatar>
          <span className="hidden sm:inline-block">
            {session?.user?.name || session?.user?.email || "Account"}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            {session?.user?.name && (
              <p className="font-medium">{session.user.name}</p>
            )}
            {session?.user?.email && (
              <p className="w-[200px] truncate text-sm text-muted-foreground">
                {session.user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuSeparator />
        {/* Collections link - only visible on mobile/small screens */}
        <Link href="/collections" className="block md:hidden">
          <DropdownMenuItem>
            <BookmarkIcon className="mr-2 h-4 w-4" />
            <span>Collections</span>
          </DropdownMenuItem>
        </Link>
        {/* Settings link */}
        <Link href="/user-dashboard">
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
        </Link>
        {RENDER_DASHBOARD && (
          <DropdownMenuItem
            onClick={() => (window.location.href = config.auth.callbackUrl)}
          >
            <User className="mr-2 h-4 w-4" />
            <span>Dashboard</span>
          </DropdownMenuItem>
        )}
        {RENDER_BILLING && (
          <DropdownMenuItem onClick={handleBilling}>
            <CreditCard className="mr-2 h-4 w-4" />
            <span>Billing</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
