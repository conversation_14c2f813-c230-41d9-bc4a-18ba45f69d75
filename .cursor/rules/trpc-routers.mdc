---
description: 
globs: server/routers/*.ts
alwaysApply: false
---
When working with trpc routes, please follow these guidelines:
- Input types should be defined in `types/models/<name>.ts` (see [lead.ts](mdc:types/models/lead.ts) for examples)
- All routes should use buildProcedure from [trpc.ts](mdc:server/trpc.ts) to build the procedure. If not already defined use type=public procedures for APIs that should be available to non logged in users (very rare use-case). Use type=protected procedures for APIs that should be available to anyone who is logged in. Use a common sense rate limit if not defined already. Use type=paid procedures for APIs that are exclusively available to paid users. See [app-info.md](mdc:.docs/app-info.md) for more context on what each type of user should do.
- When modifying multiple Mongo/Mongoose records, use a transaction.
- When creating an entity, send a discord samples webhook message (see [discord.ts](mdc:libs/discord.ts) for context). Send a single string with an emoji to start and include basic info.

Example router: [lead.ts](mdc:server/routers/lead.ts)