---
description: Guidelines for managing environment variables in the project
globs: 
alwaysApply: false
---
# Environment Variables Management

This rule provides guidelines for managing environment variables in the project using Zod for type-safe environment variable validation.

## Core Principles

1. All environment variables must be validated using Zod schemas
2. Use descriptive names with clear purposes
3. Provide default values when appropriate
4. The source of truth for all environment variables is [env.ts](mdc:libs/env.ts)

## Naming Conventions

1. Use UPPER_SNAKE_CASE for environment variable names
2. Suffix boolean flags with `_FLAG`
3. Use descriptive prefixes for related variables (e.g., `STRIPE_`, `GOOGLE_`)

## Example Implementation

```ts

// Main environment schema
const envSchema = z
  .object({
    API_KEY: z.string(),
    DATABASE_URL: z.string(),
  });

// Export parsed environment
const env = envSchema.parse(process.env);
export default env;
```

## Best Practices

1. Always validate environment variables at startup
2. Use `.default()` for optional variables with sensible defaults
3. Transform string values to appropriate types (boolean, number, etc.)
4. Document the purpose of each variable in comments

## Common Patterns

Required API Keys:
```ts
const PLATFORM_API_KEY = z.string().min(1, "API key is required");
```


## Security Considerations

1. Never commit sensitive values to version control
2. Use `.env.example` for documentation
3. Validate all sensitive variables
4. Use appropriate secret management in production 
5. We can't use the environment variables in client components, only backend logic or server components.