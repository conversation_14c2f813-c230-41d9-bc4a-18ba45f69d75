---
description: 
globs: components/onboarding/**/*.tsx,components/onboarding/*.tsx,components/onboarding/*.ts
alwaysApply: false
---
When creating or updating an onboarding step, use all of this information for ideal syntax and actions to taken:
- The onboarding step id must be added to onboardingSchema in [user.ts](mdc:types/models/user.ts)
- The onboarding step id must be added to userSchema in [User.ts](mdc:models/User.ts)
- There should be a onboarding step component (use [WelcomeOnboardingStep.tsx](mdc:components/onboarding/WelcomeOnboardingStep.tsx) as an example)
- The new onboarding step component should be added to [OnboardingWizard.tsx](mdc:components/onboarding/OnboardingWizard.tsx)
- The total step count in [onboarding-config.ts](mdc:components/onboarding/onboarding-config.ts) should be updated