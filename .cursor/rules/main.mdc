---
description: 
globs: 
alwaysApply: true
---
## MAIN

Analyze our problem(s), goal(s), code, and full context before formulating a plan and executing. Think deeply, carefully, methodically, and long. Take your time.

Once you have analyzed fully, formulate a flawless simple but effective plan, then execute it perfectly. Simple and straightforward. 

Maintain code quality, maintainability, scalability, modularity, low coupling, and follow best practices. Make sure code is clean and easily understandable. 

I prefer you to put new code (classes/functionality) in new files instead of coupling and cluttering 1 large file. Avoid large 1000+ line files AT ALL COSTS. 

Always put new classes in their own file.

Make sure there isn't already an existing file with the code/class/functionality you need before making a new one. 

Carefully consider existing files and possible ways you can leverage them, abstract things, and use them in multiple places before just writing new code for everything. 

Prefer early returns over nesting. Avoid multi level nesting. Move nested code to outside function/file as needed to keep code easily readable, non-nested, and clean.

Avoid magic numbers. Instead put them in a relevant constants file and reference them from there instead of hardcoding.

Consider ways to proactively refactor, clean things up, modularize, and make things make sense code wise and project file structure wise. Make recommendations if you find we should make certain adjustments before adding more features to make sure our project is following best practices and is scalable.

DONT run any commands that run anything! This includes our main program, scripts or anything else -- Prompt me to do it and I will do that myself.

USE MACBOOK COMPATIBLE CONSOLE COMMANDS. Do not use windows commands.

If you need more information or have questions you need answered before coming up with a proper plan and execution, request it and don't change code yet until I give you everything you need. It is way better to ask more questions when unsure than to start refactoring.

Do not leave old unused code lying around. We should clean up the project as we go. If you are unsure if things should be removed due to backwards compatability, ask me first.


## Feature Roadmap and Project Goals
- Always reference @feature_roadmap.md before implementing new features
- Check if the feature is already on the roadmap and what stage it's in
- Update @feature_roadmap.md after implementing a feature by marking it as completed, moving it to current state
- Ensure all implementations align with the project vision in @project_goals.md
- If a new feature isn't on the roadmap, suggest adding it before implementation

## Changelog Management
- Record all significant changes in docs/changelog.md using the following format:
[CHANGE]
Added
- Description of new features
Changed
- Description of changes to existing functionality
Fixed
- Description of any bug fixes

- Reference the changelog when discussing previous implementations
- When implementing a feature, check the changelog for related previous work
- After completing implementation, add an entry to the changelog
- Include ticket/issue numbers in changelog entries when applicable

## Implementation Workflow
1. Check @project_goals.md to understand how the feature fits into the overall vision
2. Review @feature_roadmap.md to confirm priority and implementation details
3. Check @changelog.md for any related previous work or dependencies
4. Implement the feature following the project's modular structure
5. Update @feature_roadmap.md to mark the feature as completed
6. Add an entry to @changelog.md documenting the implementation. Run a CLI command to get the correct date prior to creating the entry.
7. Suggest related features or next steps based on the roadmap

## Code Organization
- When creating new files, ensure they follow the established project structure
- Add appropriate references to documentation files in code comments
- Include links to relevant sections of project_goals.md or feature_roadmap.md in complex implementations 
- Put any temp or md files you don't have a folder for in the "temp" folder. Do not put them in the base project folder.