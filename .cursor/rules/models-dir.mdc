---
description: 
globs: models/*.ts
alwaysApply: false
---
When creating or updating a model file, use this information about the ideal syntax for this file:
- `TType` type at the top of the file with all of the unique attributes the type contains.
- `IType` interface which extends `TType` and `Document`
- Mongoose schema with timestamps and `toJSON: { virtuals: true }`
- Export model, but check if it already exists before registering
- We should have a partner file in `types/models/<model>.ts` which will contain Zod create/update request schemas, and a Zod schema for converting the mongoose document into a typed object for use in the frontend.

Example model file: [Lead.ts](mdc:models/Lead.ts)
Example zod file: [lead.ts](mdc:types/models/lead.ts) 

