import { router, buildProcedure, addUserToContext } from "../trpc";
import connectMongo from "@/libs/mongoose";
import { getRateLimit } from "@/utils/redis/rateLimits";
import { updateOnboardingStepSchema } from "@/types/models/user";

export const onboardingRouter = router({
  updateStep: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "onboarding:updateStep",
    }),
  })
    .use(addUserToContext)
    .input(updateOnboardingStepSchema)
    .mutation(async ({ input, ctx }) => {
      const { user } = ctx;
      await connectMongo();

      // Update the onboarding step
      user.onboarding = {
        completed: input.completed,
        step: input.step,
      };
      await user.save();

      return {
        success: true,
        currentStep: input.step,
      };
    }),
});
