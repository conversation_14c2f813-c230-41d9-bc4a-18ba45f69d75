import { router, buildProcedure } from "../trpc";
import { getRateLimit } from "@/utils/redis/rateLimits";
import { getRoles } from "@/lib/database";

export const contentRouter = router({
  getRoles: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "content:getRoles",
    }),
  }).query(async () => {
    return await getRoles();
  }),
});
