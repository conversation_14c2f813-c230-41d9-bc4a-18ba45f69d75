import { z } from "zod";
import { router, buildProcedure } from "../trpc";
import env from "@/libs/env";
import connectMongo from "@/libs/mongoose";
import { getRateLimit } from "@/utils/redis/rateLimits";
import Lead from "@/models/Lead";
import { DISCORD_SAMPLES_WEBHOOK } from "@/libs/discord";

// Schema for lead creation
const upsertLeadSchema = z.object({
  email: z.string().email(),
});

export const leadRouter = router({
  upsertLead: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 2,
      overSeconds: 120,
      prefix: "lead:upsert",
    }),
  })
    .input(upsertLeadSchema)
    .mutation(async ({ input }) => {
      if (!env.WAITLIST_MODE_FLAG) {
        throw new Error("Waitlist is not enabled");
      }

      await connectMongo();

      // Here you can add your own logic
      // For instance, sending a welcome email (use the the sendEmail helper function from /libs/resend)
      // For instance, saving the lead in the database (uncomment the code below)

      const lead = await Lead.findOne({ email: input.email });
      if (!lead) {
        await Lead.create({ email: input.email });
      }

      await DISCORD_SAMPLES_WEBHOOK.send(`📝 New Lead: ${input.email}`);

      return {};
    }),
});
