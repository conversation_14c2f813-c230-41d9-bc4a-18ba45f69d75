import { z } from "zod";
import { router, buildProcedure } from "../trpc";
import { getRateLimit } from "@/utils/redis/rateLimits";
import Collection from "@/models/Collection";
import Task from "@/models/Task";
import connectMongo from "@/libs/mongoose";
import { TRPCError } from "@trpc/server";
import {
  createCollectionSchema,
  updateCollectionSchema,
} from "@/types/models/collection";
import { DISCORD_SAMPLES_WEBHOOK } from "@/libs/discord";
import { ObjectId } from "mongodb";

export const collectionsRouter = router({
  getUserCollectionsCount: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "collections:getUserCollectionsCount",
    }),
  }).query(async ({ ctx }) => {
    const userData = await ctx.getUserData();

    if (!userData?.user) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User not found",
      });
    }

    await connectMongo();

    const count = await Collection.countDocuments({
      userId: userData.user._id,
    });

    return { count };
  }),

  getUserCollections: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 20,
      overSeconds: 60,
      prefix: "collections:getUserCollections",
    }),
  })
    .input(
      z.object({
        limit: z.number().min(1).max(50).optional().default(10),
      })
    )
    .query(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      const collections = await Collection.find({
        userId: userData.user._id,
      })
        .sort({ updatedAt: -1 }) // Most recently updated first
        .limit(input.limit)
        .lean();

      return collections.map((collection) => ({
        id: collection._id.toString(),
        name: collection.name,
        description: collection.description,
        tasks: collection.tasks.map((task: ObjectId) => task.toString()),
        updatedAt: collection.updatedAt,
        createdAt: collection.createdAt,
        isPublic: collection.isPublic,
        icon: collection.icon,
        color: collection.color,
      }));
    }),

  addTaskToCollection: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 20,
      overSeconds: 60,
      prefix: "collections:addTaskToCollection",
    }),
  })
    .input(
      z.object({
        taskId: z.string(),
        collectionId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      // Verify the task exists
      const task = await Task.findById(input.taskId);
      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Task not found",
        });
      }

      // Verify the collection exists and belongs to the user
      const collection = await Collection.findOne({
        _id: input.collectionId,
        userId: userData.user._id,
      });

      if (!collection) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Collection not found",
        });
      }

      // Check if the task is already in the collection
      const taskExists = collection.tasks.some(
        (taskId: ObjectId) => taskId.toString() === task._id.toString()
      );

      if (taskExists) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Task is already in this collection",
        });
      }

      // Add the task to the collection
      collection.tasks.push(task._id);
      await collection.save();

      return {
        success: true,
        message: "Task added to collection successfully",
      };
    }),

  removeTaskFromCollection: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 20,
      overSeconds: 60,
      prefix: "collections:removeTaskFromCollection",
    }),
  })
    .input(
      z.object({
        taskId: z.string(),
        collectionId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      // Verify the task exists
      const task = await Task.findById(input.taskId);
      if (!task) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Task not found",
        });
      }

      // Verify the collection exists and belongs to the user
      const collection = await Collection.findOne({
        _id: input.collectionId,
        userId: userData.user._id,
      });

      if (!collection) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Collection not found",
        });
      }

      // Check if the task is in the collection
      const taskIndex = collection.tasks.findIndex(
        (taskId: ObjectId) => taskId.toString() === task._id.toString()
      );

      if (taskIndex === -1) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Task is not in this collection",
        });
      }

      // Remove the task from the collection
      collection.tasks.splice(taskIndex, 1);
      await collection.save();

      return {
        success: true,
        message: "Task removed from collection successfully",
      };
    }),

  createCollection: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "collections:createCollection",
    }),
  })
    .input(createCollectionSchema)
    .mutation(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      try {
        // If taskId is provided, verify the task exists first
        if (input.taskId) {
          const task = await Task.findById(input.taskId);
          if (!task) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Task not found",
            });
          }
        }

        const collection = await Collection.create({
          ...input,
          userId: userData.user._id,
          tasks: input.taskId ? [input.taskId] : [], // Add task if provided
        });

        // Send discord notification
        await DISCORD_SAMPLES_WEBHOOK.send(
          `📂 New Collection created: "${collection.name}" by ${userData.user.email}${input.taskId ? " (with task auto-added)" : ""}`
        );

        return {
          id: collection._id.toString(),
          name: collection.name,
          description: collection.description,
          tasks: collection.tasks.map((task: any) => task.toString()),
          updatedAt: collection.updatedAt,
          createdAt: collection.createdAt,
          isPublic: collection.isPublic,
          icon: collection.icon,
          color: collection.color,
        };
      } catch (error: any) {
        if (error.code === 11000) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "You already have a collection with this name",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create collection",
        });
      }
    }),

  updateCollection: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "collections:updateCollection",
    }),
  })
    .input(
      z.object({
        id: z.string(),
        data: updateCollectionSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      try {
        const collection = await Collection.findOneAndUpdate(
          { _id: input.id, userId: userData.user._id },
          { $set: input.data },
          { new: true }
        );

        if (!collection) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Collection not found",
          });
        }

        return {
          id: collection._id.toString(),
          name: collection.name,
          description: collection.description,
          tasks: collection.tasks.map((task: ObjectId) => task.toString()),
          updatedAt: collection.updatedAt,
          createdAt: collection.createdAt,
          isPublic: collection.isPublic,
          icon: collection.icon,
          color: collection.color,
        };
      } catch (error: any) {
        if (error.code === 11000) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "You already have a collection with this name",
          });
        }
        throw error;
      }
    }),

  deleteCollection: buildProcedure({
    type: "protected",
    rateLimit: getRateLimit({
      requests: 10,
      overSeconds: 60,
      prefix: "collections:deleteCollection",
    }),
  })
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const userData = await ctx.getUserData();

      if (!userData?.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User not found",
        });
      }

      await connectMongo();

      const collection = await Collection.findOneAndDelete({
        _id: input.id,
        userId: userData.user._id,
      });

      if (!collection) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Collection not found",
        });
      }

      return { success: true };
    }),
});
