import { router, buildProcedure } from "../trpc";
import { getRateLimit } from "@/utils/redis/rateLimits";
import {
  unifiedSearchSchema,
  unifiedSearchResponseSchema,
} from "@/types/models/search";
import { TRPCError } from "@trpc/server";
import { executeUnifiedSearch } from "@/lib/search-service";
import { sendSearchNotification } from "@/utils/discord/notifications";
import { headers } from "next/headers";

export const searchRouter = router({
  searchTasks: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 20,
      overSeconds: 60,
      prefix: "search:unified-tasks",
    }),
  })
    .input(unifiedSearchSchema)
    .output(unifiedSearchResponseSchema)
    .query(async ({ input, ctx }) => {
      try {
        const result = await executeUnifiedSearch(input);

        // Send Discord notification for search events (only for actual queries, not browsing)
        if (input.query.trim().length > 0) {
          const forwardedFor = headers().get("x-forwarded-for");
          const ip = forwardedFor || "127.0.0.1";
          const userAgent = headers().get("user-agent") || "Unknown";

          // Get user data if available
          const userData = await ctx.getUserData();
          const userEmail = userData?.user?.email;

          await sendSearchNotification({
            query: input.query,
            resultsFound: result.results.length,
            totalCount: result.totalCount,
            hasNextPage: result.hasNextPage,
            roleFilter: input.roleFilter,
            minScore: input.minScore,
            userEmail,
            userIp: ip,
            userAgent,
          });
        }

        return result;
      } catch (error) {
        console.error("Unified search error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Search failed. Please try again.",
          cause: error,
        });
      }
    }),
});
