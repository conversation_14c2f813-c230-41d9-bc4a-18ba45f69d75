import { z } from "zod";
import { buildProcedure, router } from "../trpc";
import { getRateLimit } from "@/utils/redis/rateLimits";

export const exampleRouter = router({
  hello: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 5,
      overSeconds: 60,
      prefix: "example:hello",
    }),
  })
    .input(z.object({ text: z.string().nullish() }).nullish())
    .query(({ input }) => {
      return {
        greeting: `Hello ${input?.text ?? "world"}`,
      };
    }),
});
