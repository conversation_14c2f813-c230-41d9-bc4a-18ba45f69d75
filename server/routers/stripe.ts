import { z } from "zod";
import { router, buildProcedure } from "../trpc";
import {
  createCheckout,
  createCustomer,
  createCustomerPortal,
} from "@/libs/stripe";
import { TRPCError } from "@trpc/server";
import { RedirectResponse } from "@/utils/trpc/redirect";
import config from "@/config";
import { getRateLimit } from "@/utils/redis/rateLimits";

// Schema for checkout creation - matches the API route schema
const createCheckoutSchema = z.object({
  priceId: z.string(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
  mode: z.enum(["payment", "subscription"]),
  couponId: z.string().optional(),
});

// Schema for customer portal creation
const createPortalSchema = z.object({
  returnUrl: z.string().url(),
});

export const stripeRouter = router({
  createCheckout: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 2,
      overSeconds: 120,
      prefix: "stripe:createCheckout",
    }),
  })
    .input(createCheckoutSchema)
    .mutation(async ({ input, ctx }) => {
      const { priceId, mode, successUrl, cancelUrl, couponId } = input;

      // Get user data if they're logged in
      const userData = await ctx.getUserData();
      const user = userData?.user;

      if (!user) {
        return { redirect: config.auth.loginUrl } satisfies RedirectResponse;
      }

      let customerId = user?.customerId;

      try {
        if (!customerId) {
          const customer = await createCustomer({
            email: user.email,
            userId: user._id.toString(),
          });
          user.customerId = customer.id;
          await user.save();
          customerId = customer.id;
        }
        const stripeSessionURL = await createCheckout({
          priceId,
          mode,
          successUrl,
          cancelUrl,
          couponId,
          // Only pass user-related data if user exists
          clientReferenceId: user._id.toString(),
          user: {
            customerId,
            email: user.email,
            userId: user._id.toString(),
          },
        });

        return { url: stripeSessionURL };
      } catch (error) {
        console.error(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create checkout session",
          cause: error,
        });
      }
    }),

  createPortal: buildProcedure({
    type: "protected", // This requires authentication
    rateLimit: getRateLimit({
      requests: 4,
      overSeconds: 120,
      prefix: "stripe:createPortal",
    }),
  })
    .input(createPortalSchema)
    .mutation(async ({ input, ctx }) => {
      const { returnUrl } = input;
      const userData = await ctx.getUserData();

      if (!userData || !userData.user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Not signed in",
        });
      }

      const user = userData.user;

      if (!user.customerId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "You don't have a billing account yet. Make a purchase first.",
        });
      }

      try {
        const stripePortalUrl = await createCustomerPortal({
          customerId: user.customerId,
          returnUrl,
        });

        return { url: stripePortalUrl };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create customer portal",
          cause: error,
        });
      }
    }),
});
