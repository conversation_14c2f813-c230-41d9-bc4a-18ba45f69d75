import { z } from "zod";
import crypto from "crypto";
import { router, buildProcedure } from "../trpc";
import connectMongo from "@/libs/mongoose";
import Solution from "@/models/Solution";
import { redis } from "@/utils/redis/client";
import { getRateLimit } from "@/utils/redis/rateLimits";
import { headers } from "next/headers";
import { sendPromptUsageNotification } from "@/utils/discord/notifications";

const trackSolutionViewSchema = z.object({
  solutionId: z.string().min(1),
  userAgent: z.string().optional(),
  fingerprint: z.string().optional(),
});

const trackSolutionUsageSchema = z.object({
  solutionId: z.string().min(1),
  userAgent: z.string().optional(),
  fingerprint: z.string().optional(),
});

/**
 * Creates a hash for user deduplication based on IP, userAgent, and fingerprint
 */
function createUserHash(userAgent?: string, fingerprint?: string): string {
  // Get IP address from headers
  const forwardedFor = headers().get("x-forwarded-for");
  const ip = forwardedFor || "127.0.0.1";

  // Create user hash for deduplication
  const userData = `${ip}:${userAgent || ""}:${fingerprint || ""}`;
  return crypto.createHash("sha256").update(userData).digest("hex");
}

export const analyticsRouter = router({
  trackSolutionView: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 50,
      overSeconds: 600,
      prefix: "analytics:track-solution-view",
    }),
  })
    .input(trackSolutionViewSchema)
    .mutation(async ({ input }) => {
      const { solutionId, userAgent, fingerprint } = input;

      // Create viewer hash for deduplication
      const viewerHash = createUserHash(userAgent, fingerprint);

      // Redis key for 10-minute deduplication
      const viewerKey = `solution_view:${solutionId}:${viewerHash}`;

      try {
        // Check if this viewer has viewed this solution in the last 10 minutes
        const lastViewed = await redis.get(viewerKey);

        if (lastViewed) {
          // Already viewed within 10 minutes, don't count again
          return { success: true };
        }

        // Connect to MongoDB and verify solution exists
        await connectMongo();
        const solution = await Solution.findById(solutionId);

        if (!solution) {
          throw new Error("Solution not found");
        }

        // Increment view count atomically
        await Solution.findByIdAndUpdate(
          solutionId,
          { $inc: { viewCount: 1 } },
          { new: true }
        );

        // Set Redis key with 10-minute TTL (600 seconds)
        await redis.setex(viewerKey, 600, Date.now().toString());

        return { success: true };
      } catch (error) {
        console.error("Error tracking solution view:", error);
        throw new Error("Failed to track view");
      }
    }),

  trackSolutionUsage: buildProcedure({
    type: "public",
    rateLimit: getRateLimit({
      requests: 50,
      overSeconds: 600,
      prefix: "analytics:track-solution-usage",
    }),
  })
    .input(trackSolutionUsageSchema)
    .mutation(async ({ input, ctx }) => {
      const { solutionId, userAgent, fingerprint } = input;

      // Create user hash for deduplication
      const userHash = createUserHash(userAgent, fingerprint);

      // Redis key for 10-minute deduplication
      const usageKey = `solution_usage:${solutionId}:${userHash}`;

      try {
        // Check if this user has used this solution in the last 10 minutes
        const lastUsed = await redis.get(usageKey);

        if (lastUsed) {
          // Already used within 10 minutes, don't count again
          return { success: true };
        }

        // Connect to MongoDB and verify solution exists
        await connectMongo();
        const solution = await Solution.findById(solutionId);

        if (!solution) {
          throw new Error("Solution not found");
        }

        // Increment usage count atomically
        const updatedSolution = await Solution.findByIdAndUpdate(
          solutionId,
          { $inc: { usageCount: 1 } },
          { new: true }
        );

        // Set Redis key with 10-minute TTL (600 seconds)
        await redis.setex(usageKey, 600, Date.now().toString());

        // Send Discord notification for prompt usage
        const forwardedFor = headers().get("x-forwarded-for");
        const ip = forwardedFor || "127.0.0.1";

        // Get user data if available
        const userData = await ctx.getUserData();
        const userEmail = userData?.user?.email;

        await sendPromptUsageNotification({
          solutionId,
          solutionName: updatedSolution?.name || "Unknown",
          usageCount: updatedSolution?.usageCount || 0,
          userEmail,
          userIp: ip,
          userAgent,
        });

        return { success: true };
      } catch (error) {
        console.error("Error tracking solution usage:", error);
        throw new Error("Failed to track usage");
      }
    }),
});
