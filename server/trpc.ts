import { initTR<PERSON>, TRPCError } from "@trpc/server";
import superjson from "superjson";
import { getServerSession, Session } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import User, { IUser } from "@/models/User";
import connectMongo from "@/libs/mongoose";
import { MongoDocumentResponse } from "@/types/mongo-helpers";
import { headers } from "next/headers";
import { Ratelimit } from "@upstash/ratelimit";

// Context type definition
export type Context = {
  getUserData: () => Promise<{
    user?: MongoDocumentResponse<IUser>;
    session?: Session;
  } | null>;
};

// Context creator function
export const createContext = async (): Promise<Context> => {
  let cachedUserData:
    | {
        user?: MongoDocumentResponse<IUser>;
        session: Session;
      }
    | null
    | undefined = undefined;

  return {
    getUserData: async () => {
      if (cachedUserData !== undefined) {
        return cachedUserData;
      }

      const session = await getServerSession(authOptions);
      if (!session) {
        cachedUserData = null;
        return cachedUserData;
      }

      await connectMongo();
      const user = await User.findById(session.user.id);
      if (!user) {
        cachedUserData = { session, user: undefined };
        return cachedUserData;
      }

      cachedUserData = { user, session };
      return cachedUserData;
    },
  };
};

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// Middleware for requiring authentication
const isAuthed = t.middleware(async ({ ctx, next }) => {
  const userData = await ctx.getUserData();

  if (!userData || !userData.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: userData,
  });
});

// Middleware for requiring paid access
const isPaidUser = t.middleware(async ({ ctx, next }) => {
  const userData = await ctx.getUserData();

  if (!userData || !userData.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  if (!userData.user.hasAccess) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "User does not have access",
    });
  }

  return next({
    ctx: userData,
  });
});

export const addUserToContext = t.middleware(async ({ ctx, next }) => {
  const userData = await ctx.getUserData();

  const user = userData?.user;
  if (!user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      user,
    },
  });
});

// Rate limiting middleware generator
export const createRateLimitMiddleware = (rateLimiter: Ratelimit) => {
  return t.middleware(async ({ path, next }) => {
    const ip = headers().get("x-forwarded-for") ?? "127.0.0.1";
    // Include the procedure path in the identifier to make rate limits independent per procedure
    const identifier = `${ip}:${path}`;
    const { success } = await rateLimiter.limit(identifier);

    if (!success) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: "Rate limit exceeded",
      });
    }

    return next();
  });
};

// Export reusable router and procedure helpers
export const router = t.router;

export const buildProcedure = ({
  type,
  rateLimit,
}: {
  type: "public" | "protected" | "paid";
  rateLimit: Ratelimit;
}) => {
  const procedure = t.procedure.use(createRateLimitMiddleware(rateLimit));
  if (type === "protected") {
    return procedure.use(isAuthed);
  } else if (type === "paid") {
    return procedure.use(isPaidUser);
  }
  return procedure;
};
