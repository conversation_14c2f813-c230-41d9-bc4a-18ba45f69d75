import { NextRequest, NextResponse } from "next/server";
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";
import env from "@/libs/env";

const redis = new Redis({
  url: env.UPSTASH_REDIS_REST_URL,
  token: env.UPSTASH_REDIS_REST_TOKEN,
});

const getRateLimit = ({
  requests,
  overSeconds,
}: {
  requests: number;
  overSeconds: number;
}) => {
  return new Ratelimit({
    redis: redis,
    limiter: Ratelimit.slidingWindow(requests, `${overSeconds} s`),
  });
};

const defaultRateLimit = getRateLimit({ requests: 5, overSeconds: 60 });
const leadRateLimit = getRateLimit({ requests: 1, overSeconds: 120 });
const matchRateLimitRecord = (pathname: string): Ratelimit | undefined => {
  for (const key in RATE_LIMIT_RECORD) {
    if (pathname.startsWith(key)) {
      return RATE_LIMIT_RECORD[key];
    }
  }
  return undefined;
};

const RATE_LIMIT_RECORD: Record<string, Ratelimit> = {
  "/api/lead": leadRateLimit,
};

export default async function middleware(request: NextRequest) {
  const ip = request.ip ?? "127.0.0.1";

  const rateLimiter =
    matchRateLimitRecord(request.nextUrl.pathname) ?? defaultRateLimit;

  const { success } = await rateLimiter.limit(ip);

  return success
    ? NextResponse.next()
    : NextResponse.redirect(new URL("/blocked", request.url));
}

export const config = {
  matcher: ["/api/lead"],
};
