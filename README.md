# DoThisTaskAI

## Project File Structure

```
dothistaskai/
├── app/                          # Next.js App Router pages and layouts
│   ├── (authenticated)/          # Protected routes requiring auth
│   ├── api/                      # API routes (NextAuth, tRPC, webhooks)
│   ├── auth/                     # NextAuth setup
│   ├── blog/                     # Blog system with articles and categories
│   └── [domain]/                 # Multiple folders split by domain
├── components/                   # React components
│   ├── [domain]/                 # Multiple folders split by domain
│   └── ui/                       # Reusable UI components (shadcn/ui)
├── docs/                         # Project documentation
├── hooks/                        # Custom React hooks
├── lib/                          # Utility libraries and services
├── libs/                         # External service integrations
├── models/                       # Database models (Mongoose)
├── public/                       # Static assets
├── scripts/                      # Build and data scripts
├── server/                       # tRPC server routers (backend logic!)
├── temp/                         # Temporary files
├── types/                        # TypeScript type definitions
└── utils/                        # Utility functions
```

Will notes: 
- there arent many differences between `lib`, `libs` and `utils`, we probably should combine them.

## Tech Stack

### Next.js App Router
- Built with Next.js 14+ using the App Router pattern
- Route groups `(authenticated)` for protected routes
- API routes in `app/api/` for NextAuth, tRPC, and webhooks
- Server and client components 
- Docs: [nextjs.org/docs/app](https://nextjs.org/docs/app)

### tRPC
- All non-webhook backend routes implemented with tRPC for type-safe APIs
- Backend routers located in `server/routers/`
- Frontend client setup in `utils/trpc/`
- Type-safe API calls with automatic TypeScript inference
- Docs: [trpc.io](https://trpc.io)

### Authentication
- NextAuth.js for authentication with multiple providers
- Configuration in `libs/next-auth.ts`
- Protected routes using route groups and middleware
- User session management and role-based access
- Docs: [next-auth.js.org](https://next-auth.js.org)

### Database
- MongoDB with Mongoose ODM for data modeling
- Models located in `models/` directory
- Connection setup in `libs/mongoose.ts`
- TypeScript interfaces in `types/models/`
- Docs: [mongoosejs.com](https://mongoosejs.com)

### UI Components
- Shadcn/ui component library with Radix UI primitives
- Custom components in `components/ui/`
- We should always use colors from globals.css instead of hardcoding
- Tailwind CSS for styling with custom design system
- Responsive design with mobile-first approach
- Docs: [ui.shadcn.com](https://ui.shadcn.com)

### Search & AI
- Pinecone vector database for semantic search
- OpenAI GPT integration for AI features
- Search service in `lib/search-service.ts`
- Vector embeddings for content discovery
- Docs: [pinecone.io](https://pinecone.io), [openai.com](https://openai.com)

### Payment Processing
- Stripe integration for payments and subscriptions
- Webhook handling in `app/api/webhook/stripe/`
- Pricing configuration in `libs/pricing.ts`
- Checkout components and payment flows
- Docs: [stripe.com/docs](https://stripe.com/docs)

### Analytics & Monitoring
- PostHog for product analytics
- Error tracking and user behavior analysis
- Custom event tracking throughout the application
- A/B testing and feature flag management
- Docs: [posthog.com](https://posthog.com)

### Email & Communication
- Resend for transactional emails
- Email templates and delivery management
- Lead capture and notification systems
- Docs: [resend.com](https://resend.com)

### Development Tools
- TypeScript for type safety across the stack
- ESLint and Prettier for code formatting
- Custom hooks in `hooks/` for reusable logic
- Utility functions in `lib/`, `libs/`, and `utils/`
- PNPM for package management

## Local Development

### Prerequisites
- Node.js 18+ and PNPM
- MongoDB database (local or cloud)
- Environment variables configured (see `.env.example`)

### Setup
```bash
# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env.local

# Start development server
pnpm dev
```

### Environment Variables
Required environment variables for local development:
- `MONGODB_URI` - MongoDB connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret key
- `NEXTAUTH_URL` - Local development URL (http://localhost:3000)
- `OPENAI_API_KEY` - OpenAI API key for AI features
- `PINECONE_API_KEY` - Pinecone vector database API key
- `POSTHOG_API_KEY` - PostHog analytics API key
- `STRIPE_SECRET_KEY` - Stripe secret key for payments
- `RESEND_API_KEY` - Resend email service API key

## Architecture Overview

### Server Components
We should prioritize server components over client components because:
- it saves us from building an entire API with rate limiting, row level security, etc..
- better for SEO
- very easy to set up cache
Use-cases where we use client components:
- anything with client interactivity
- forms

### TRPC Procedure Design
- These are our backend APIs
- We should always add rate limiting
- For APIs that require auth, we should check that too
- We have a buildProcedure method in server/trpc.ts which builds a public/authenticated procedure with rate limiting
- Authorization (row level security) must be done manually in each TRPC procedure (or put in common code somewhere)
- All backend logic (minus webhooks) should be implemented in trpc routers, not typical NextJS APIs

## Features

### Onboarding System

The onboarding system provides a guided wizard experience for new users to get familiar with the platform. It's implemented as a multi-step process that users must complete before accessing the main dashboard.

#### Architecture Overview
- **Wizard Component**: `OnboardingWizard` serves as the main container that renders the appropriate step component
- **Step Components**: Individual step components (e.g., `WelcomeOnboardingStep`, `FinishedOnboardingStep`) contain the content for each onboarding phase
- **Controls**: `OnboardingWizardControls` provides navigation buttons and handles step progression
- **Progress Indicator**: `OnboardingFooter` displays a visual progress bar showing completion status
- **Configuration**: `onboarding-config.ts` defines the total number of steps and other wizard settings

#### Step Management
- **Database Storage**: User onboarding progress is stored in the `User` model with `onboarding.completed` and `onboarding.step` fields
- **Step Types**: Currently supports "welcome" and "finished" steps, defined in the `OnboardingStepId` type
- **Type Safety**: All step operations use Zod schemas for validation and TypeScript type inference
- **TRPC Integration**: Step updates are handled through the `onboarding.updateStep` procedure with rate limiting

#### User Flow
1. **Entry Point**: New users are automatically redirected to the onboarding wizard when `user.onboarding.completed === false`
2. **Step Rendering**: The wizard dynamically renders the appropriate step component based on the user's current step
3. **Navigation**: Users progress through steps using the "Next" button, which triggers validation and database updates
4. **Completion**: Upon finishing all steps, users are redirected to the main dashboard

#### Adding New Steps
To add a new onboarding step:
1. Add the step ID to the `OnboardingStepId` enum in `types/models/user.ts`
2. Update the `userSchema` in `models/User.ts` to include the new step
3. Create a new step component following the pattern of existing steps
4. Add the step to the `getCurrentStep` function in `OnboardingWizard.tsx`
5. Update the total step count in `onboarding-config.ts`

#### Features
- **Responsive Design**: Works seamlessly across desktop and mobile devices
- **Progress Tracking**: Visual progress indicators help users understand their completion status
- **Rate Limiting**: API calls are protected against abuse with configurable rate limits
- **Error Handling**: Graceful error handling for failed step updates
- **Type Safety**: Full TypeScript support with Zod validation throughout the flow