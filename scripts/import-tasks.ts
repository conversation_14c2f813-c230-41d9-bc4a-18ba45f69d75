#!/usr/bin/env tsx

import { z } from "zod";
import fs from "fs/promises";
import path from "path";
import mongoose from "mongoose";
import { program } from "commander";
import dotenv from "dotenv";
import connectMongo from "../libs/mongoose";
import Task from "../models/Task";
import Solution from "../models/Solution";
import Role from "../models/Role";
import OpenAI from "openai";

// Load environment variables
dotenv.config({ path: ".env.local" });

// Environment validation schema
const envSchema = z.object({
  OPENAI_API_KEY: z.string().min(1, "OPENAI_API_KEY is required"),
  MONGODB_URI: z.string().min(1, "MONGODB_URI is required"),
});

// Validate environment variables
function validateEnvironment() {
  try {
    const env = envSchema.parse(process.env);
    console.log("✓ Environment variables validated");
    return env;
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("❌ Environment validation failed:");
      console.error("Missing or invalid environment variables:\n");

      error.errors.forEach((err) => {
        const field = err.path.join(".");
        console.error(`  🔸 ${field}: ${err.message}`);
      });

      console.error("\n💡 Make sure your .env.local file contains:");
      console.error("   OPENAI_API_KEY=your_openai_api_key_here");
      console.error("   MONGODB_URI=your_mongodb_connection_string_here");
      console.error(
        "\n🔍 Check that .env.local exists in your project root directory."
      );

      process.exit(1);
    }
    throw error;
  }
}

// Validate environment on startup
const env = validateEnvironment();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

// Zod schema for the input JSON structure
const taskInputSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
  inputs: z.array(z.string()),
  outputs: z.array(z.string()),
});

const tasksInputSchema = z.array(taskInputSchema);

type TaskInput = z.infer<typeof taskInputSchema>;

// CLI configuration
program
  .name("import-tasks")
  .description("Import tasks from JSON file and generate AI solutions")
  .requiredOption("-i, --input <file>", "Input JSON file path")
  .requiredOption("-r, --role-id <id>", "Role ID to associate with tasks")
  .option("-d, --dry-run", "Run without actually creating database entries")
  .option(
    "--overwrite",
    "Update existing tasks instead of skipping them",
    false
  )
  .parse();

const options = program.opts();

async function validateRoleExists(roleId: string): Promise<void> {
  const role = await Role.findById(roleId);
  if (!role) {
    throw new Error(`Role with ID ${roleId} not found`);
  }
  console.log(`✓ Found role: ${role.roleName}`);
}

async function readAndParseJsonFile(filePath: string): Promise<TaskInput[]> {
  try {
    const absolutePath = path.resolve(filePath);
    const fileContent = await fs.readFile(absolutePath, "utf-8");
    const jsonData = JSON.parse(fileContent);

    const validatedData = tasksInputSchema.parse(jsonData);
    console.log(
      `✓ Successfully parsed ${validatedData.length} tasks from ${filePath}`
    );

    return validatedData;
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("❌ JSON validation error:");
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join(".")}: ${err.message}`);
      });
    } else {
      console.error(`❌ Error reading file: ${error}`);
    }
    throw error;
  }
}

async function taskAlreadyExists({
  taskName,
  roleId,
}: {
  taskName: string;
  roleId: string;
}): Promise<boolean> {
  try {
    const existingTask = await Task.findOne({
      taskName,
      roles: roleId,
    });
    return !!existingTask;
  } catch (error) {
    console.error(`  ❌ Error checking for existing task: ${error}`);
    return false;
  }
}

async function generatePromptForTask(taskInput: TaskInput): Promise<string> {
  // This is the PromptCraft v4 prompt adapted for our task generation use case
  const promptGenerationPrompt = `You are PromptCraft v4, the pinnacle of prompt engineering systems. You are a highly sophisticated meta-cognitive AI. Your primary directive is to transform a user's raw, and often incomplete, task description into a flawless, comprehensive, and powerfully effective prompt or series of prompts for another Large Language Model (LLM).

You DO NOT perform the user's task. You architect the perfect instruction set for another AI to do so. Your entire existence is dedicated to this meta-task. You must be rigorous, thoughtful, and relentlessly user-focused.

#################################################################
# SPECIFIC TASK CONTEXT
#################################################################

I need you to create a prompt for the following business task:

**Task Title:** ${taskInput.title}
**Task Description:** ${taskInput.description}
**Required Inputs:** ${taskInput.inputs.join(", ")}
**Expected Outputs:** ${taskInput.outputs.join(", ")}

This task will be executed by knowledge workers who need an AI system to help them complete this specific business process. The prompt you create should be ready-to-use - meaning a knowledge worker can copy it, fill in their specific data, and get the exact outputs listed above.

#################################################################
# CRITICAL TEMPLATE VARIABLE REQUIREMENTS
#################################################################

TEMPLATE VARIABLES:
- You MUST include at least one template variable for each required input listed above
- Template variables should be formatted as: {{input name separated by spaces}}
- You MUST NOT include ANY other template variables beyond those for the required inputs
- Do NOT include template variables for dates, timestamps, automatic insertions, or any system-generated content
- Do NOT include template variables for optional fields, metadata, or anything not explicitly listed in "Required Inputs"

EXAMPLE: If the required inputs are "company description, target audience", your prompt should contain exactly these template variables: {{company description}} and {{targe audience}} - nothing else.

#################################################################
# YOUR DIRECTIVE
#################################################################

The prompt you generate must:
1. Be structured and professional with clear sections (ACT AS, CONTEXT, YOUR TASK, CONSTRAINTS, etc.)
2. Include exactly one template variable for each required input (and no others)
3. Specify the exact format and content of the expected outputs
4. Include domain expertise relevant to this type of business task
5. Have clear constraints and quality guidelines
6. Be immediately usable by a knowledge worker
7. Use snake_case for template variable names (e.g., {{team_coding_standards}} not {{description_of_team_coding_standards}})
8. Never include automatic date insertion, system variables, or any template variables beyond the required inputs

VALIDATION CHECKLIST:
- Check your template variables - there should be at least one for each required input and no others
- Verify no automatic/system template variables are included
- Ensure all required inputs have corresponding template variables

Format your response as a complete, ready-to-use prompt. Do not include any other text or comments.

Remember: You are creating a prompt FOR another AI system to execute this task, not executing the task yourself.`;

  const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
    {
      role: "user",
      content: promptGenerationPrompt,
    },
  ];

  console.log(`  📝 Generating prompt for: ${taskInput.title}`);

  try {
    const completion = await openai.chat.completions.create({
      model: "o3",
      messages,
      max_completion_tokens: 4000,
      user: "1",
    });

    const generatedPrompt = completion.choices[0]?.message?.content;
    const usage = completion.usage;

    if (usage) {
      console.log(
        `  📊 Tokens used: ${usage.total_tokens} (prompt: ${usage.prompt_tokens}, response: ${usage.completion_tokens})`
      );
    }

    if (!generatedPrompt) {
      throw new Error(`No content generated for task: ${taskInput.title}`);
    }

    return generatedPrompt;
  } catch (error) {
    console.error(`  ❌ OpenAI API Error for ${taskInput.title}:`, error);
    throw new Error(`Failed to generate prompt for task: ${taskInput.title}`);
  }
}

async function createTaskAndSolution({
  taskInput,
  roleId,
  generatedPrompt,
}: {
  taskInput: TaskInput;
  roleId: string;
  generatedPrompt: string;
}): Promise<{ taskId: string; solutionId: string }> {
  try {
    // Create the Solution first
    const solution = new Solution({
      name: taskInput.title,
      description: taskInput.description,
      prompt: generatedPrompt,
      inputs: taskInput.inputs,
      outputs: taskInput.outputs,
    });

    const savedSolution = await solution.save();
    console.log(`  ✓ Created solution: ${savedSolution.name}`);

    // Create the Task with reference to the Solution
    const task = new Task({
      taskName: taskInput.title,
      taskDescription: taskInput.description,
      prompts: {
        promptCraftV4: savedSolution._id,
      },
      roles: [roleId],
    });

    const savedTask = await task.save();
    console.log(`  ✓ Created task: ${savedTask.taskName}`);

    return {
      taskId: savedTask._id.toString(),
      solutionId: savedSolution._id.toString(),
    };
  } catch (error) {
    console.error(
      `  ❌ Error creating task/solution for ${taskInput.title}:`,
      error
    );
    throw error;
  }
}

async function updateTaskAndSolution({
  taskInput,
  roleId,
  generatedPrompt,
}: {
  taskInput: TaskInput;
  roleId: string;
  generatedPrompt: string;
}): Promise<{ taskId: string; solutionId: string }> {
  try {
    // Find existing task
    const existingTask = await Task.findOne({
      taskName: taskInput.title,
      roles: roleId,
    });

    if (!existingTask) {
      throw new Error(`Task "${taskInput.title}" not found for update`);
    }

    // Find existing solution
    const existingSolution = await Solution.findById(
      existingTask.prompts?.promptCraftV4
    );

    if (!existingSolution) {
      throw new Error(
        `Solution for task "${taskInput.title}" not found for update`
      );
    }

    // Update the Solution
    existingSolution.description = taskInput.description;
    existingSolution.prompt = generatedPrompt;
    existingSolution.inputs = taskInput.inputs;
    existingSolution.outputs = taskInput.outputs;

    const updatedSolution = await existingSolution.save();
    console.log(`  ✓ Updated solution: ${updatedSolution.name}`);

    // Update the Task
    existingTask.taskDescription = taskInput.description;
    const updatedTask = await existingTask.save();
    console.log(`  ✓ Updated task: ${updatedTask.taskName}`);

    return {
      taskId: updatedTask._id.toString(),
      solutionId: updatedSolution._id.toString(),
    };
  } catch (error) {
    console.error(
      `  ❌ Error updating task/solution for ${taskInput.title}:`,
      error
    );
    throw error;
  }
}

async function processTask({
  taskInput,
  roleId,
  isDryRun,
  isOverwrite,
}: {
  taskInput: TaskInput;
  roleId: string;
  isDryRun: boolean;
  isOverwrite: boolean;
}): Promise<void> {
  console.log(`\n🔄 Processing: ${taskInput.title}`);

  try {
    // Generate the prompt using OpenAI
    const generatedPrompt = await generatePromptForTask(taskInput);
    console.log(`  ✓ Generated prompt (${generatedPrompt.length} characters)`);

    if (isDryRun) {
      const action = isOverwrite ? "update" : "create";
      console.log(`  🏃 DRY RUN: Would ${action} task and solution`);
      console.log(
        `  📝 Generated prompt preview: ${generatedPrompt.substring(0, 100)}...`
      );
      return;
    }

    // Create or update the Task and Solution in the database
    const { taskId, solutionId } = isOverwrite
      ? await updateTaskAndSolution({
          taskInput,
          roleId,
          generatedPrompt,
        })
      : await createTaskAndSolution({
          taskInput,
          roleId,
          generatedPrompt,
        });

    const action = isOverwrite ? "updated" : "created";
    console.log(
      `  ✅ Successfully ${action} Task (${taskId}) and Solution (${solutionId})`
    );
  } catch (error) {
    console.error(`  ❌ Failed to process task: ${taskInput.title}`);
    throw error;
  }
}

async function main(): Promise<void> {
  try {
    console.log("🚀 Starting task import process...");

    // Connect to database
    await connectMongo();
    console.log("✓ Connected to database");

    // Validate role exists
    await validateRoleExists(options.roleId);

    // Read and parse the input file
    const tasks = await readAndParseJsonFile(options.input);

    if (options.dryRun) {
      console.log("\n🏃 DRY RUN MODE - No database changes will be made");
    }

    // Process each task
    console.log(`\n📋 Processing ${tasks.length} tasks...`);

    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      if (!task) {
        console.error(`❌ Task at index ${i} is undefined`);
        continue;
      }

      console.log(`\n[${i + 1}/${tasks.length}]`);

      // Check if task already exists (unless overwrite is enabled)
      if (!options.overwrite) {
        const exists = await taskAlreadyExists({
          taskName: task.title,
          roleId: options.roleId,
        });

        if (exists) {
          console.log(`⏭️  Skipping: "${task.title}" (already exists)`);
          continue;
        }
      }

      await processTask({
        taskInput: task,
        roleId: options.roleId,
        isDryRun: options.dryRun,
        isOverwrite: options.overwrite,
      });
    }

    console.log(`\n🎉 Successfully processed ${tasks.length} tasks!`);
  } catch (error) {
    console.error("❌ Import process failed:", error);
    process.exit(1);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("✓ Database connection closed");
  }
}

// Handle unhandled rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error("Fatal error:", error);
  process.exit(1);
});
