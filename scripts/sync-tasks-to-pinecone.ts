import { Pinecone } from "@pinecone-database/pinecone";
import OpenAI from "openai";
import connectMongo from "../libs/mongoose";
import Task, { type ITask } from "../models/Task";
import Role, { type IRole } from "../models/Role";
import Solution from "../models/Solution";
import {
  PineconeRecord,
  PINECONE_CONFIG,
  createPineconeMetadata,
  createPineconeRecord,
} from "../libs/pinecone";
import { z } from "zod";
import dotenv from "dotenv";

// Load environment variables from .env.local
dotenv.config({ path: ".env.local" });

// Environment validation schema
const envSchema = z.object({
  OPENAI_API_KEY: z.string().min(1, "OPENAI_API_KEY is required"),
  PINECONE_API_KEY: z.string().min(1, "PINECONE_API_KEY is required"),
  PINECONE_INDEX_NAME: z.string().min(1, "PINECONE_INDEX_NAME is required"),
});

const env = envSchema.parse(process.env);

/**
 * Initialize OpenAI client
 */
function initializeOpenAI() {
  return new OpenAI({
    apiKey: env.OPENAI_API_KEY,
  });
}

/**
 * Initialize Pinecone client and get index
 */
async function initializePinecone() {
  const pc = new Pinecone({ apiKey: env.PINECONE_API_KEY });
  const index = pc.index(env.PINECONE_INDEX_NAME);
  return { pc, index };
}

/**
 * Create Pinecone index if it doesn't exist
 */
async function ensureIndexExists() {
  const { pc } = await initializePinecone();

  try {
    // Check if index exists
    const indexList = await pc.listIndexes();
    let indexExists = false;

    if (indexList.indexes) {
      for (const index of indexList.indexes) {
        if (index && index.name === env.PINECONE_INDEX_NAME) {
          indexExists = true;
          break;
        }
      }
    }

    if (!indexExists) {
      console.log(`Creating Pinecone index: ${env.PINECONE_INDEX_NAME}`);

      await pc.createIndex({
        name: env.PINECONE_INDEX_NAME,
        dimension: PINECONE_CONFIG.EMBEDDING_DIMENSIONS,
        metric: "cosine",
        spec: {
          serverless: {
            cloud: "aws",
            region: "us-east-1",
          },
        },
      });

      console.log("✅ Index created successfully");

      // Wait for index to be ready
      console.log("Waiting for index to be ready...");
      let isReady = false;
      while (!isReady) {
        try {
          const indexDesc = await pc.describeIndex(env.PINECONE_INDEX_NAME);
          if (indexDesc.status?.ready) {
            isReady = true;
            console.log("✅ Index is ready");
          } else {
            console.log("Index still initializing...");
            await new Promise((resolve) => setTimeout(resolve, 5000));
          }
        } catch (error) {
          console.log("Waiting for index to be available...");
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }
    } else {
      console.log(`✅ Index ${env.PINECONE_INDEX_NAME} already exists`);
    }
  } catch (error) {
    console.error("Error checking/creating index:", error);
    throw error;
  }
}

/**
 * Generate embeddings for multiple texts in batches using OpenAI's text-embedding-3-small model
 */
async function generateEmbeddingsBatch(texts: string[]): Promise<number[][]> {
  const openai = initializeOpenAI();
  const allEmbeddings: number[][] = [];

  // Process texts in batches of EMBEDDING_BATCH_SIZE
  for (let i = 0; i < texts.length; i += PINECONE_CONFIG.EMBEDDING_BATCH_SIZE) {
    const batch = texts.slice(i, i + PINECONE_CONFIG.EMBEDDING_BATCH_SIZE);

    try {
      console.log(
        `Generating embeddings for batch ${Math.floor(i / PINECONE_CONFIG.EMBEDDING_BATCH_SIZE) + 1} of ${Math.ceil(texts.length / PINECONE_CONFIG.EMBEDDING_BATCH_SIZE)} (${batch.length} texts)`
      );

      const response = await openai.embeddings.create({
        input: batch,
        model: PINECONE_CONFIG.EMBEDDING_MODEL,
      });

      const batchEmbeddings = response.data.map((item) => item.embedding);
      allEmbeddings.push(...batchEmbeddings);

      // Small delay between batches to respect rate limits
      if (i + PINECONE_CONFIG.EMBEDDING_BATCH_SIZE < texts.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    } catch (error) {
      throw new Error(
        `OpenAI batch embedding generation failed for batch starting at index ${i}: ${error}`
      );
    }
  }

  return allEmbeddings;
}

/**
 * Generate embedding for a single text (kept for compatibility)
 */
async function generateEmbedding(text: string): Promise<number[]> {
  const embeddings = await generateEmbeddingsBatch([text]);
  if (embeddings.length === 0 || !embeddings[0]) {
    throw new Error("No embeddings generated");
  }
  return embeddings[0];
}

/**
 * Get tasks that need to be synced to Pinecone
 */
async function getTasksToSync(): Promise<ITask[]> {
  const query = {
    $or: [
      { mostRecentSyncToVectorDatabase: { $exists: false } },
      { $expr: { $gt: ["$updatedAt", "$mostRecentSyncToVectorDatabase"] } },
    ],
  };

  return Task.find(query).populate("roles");
}

/**
 * Upsert records to Pinecone in batches
 */
async function upsertRecordsToPinecone(records: PineconeRecord[]) {
  const { index } = await initializePinecone();

  // Process in batches
  for (let i = 0; i < records.length; i += PINECONE_CONFIG.BATCH_SIZE) {
    const batch = records.slice(i, i + PINECONE_CONFIG.BATCH_SIZE);

    console.log(
      `Upserting batch ${Math.floor(i / PINECONE_CONFIG.BATCH_SIZE) + 1} of ${Math.ceil(records.length / PINECONE_CONFIG.BATCH_SIZE)} (${batch.length} records)`
    );

    await index.namespace(PINECONE_CONFIG.NAMESPACE).upsert(batch);

    // Small delay between batches to avoid rate limiting
    if (i + PINECONE_CONFIG.BATCH_SIZE < records.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }
}

/**
 * Update task records with sync timestamp
 */
async function updateTaskSyncTimestamp(taskIds: string[]) {
  const now = new Date();

  await Task.updateMany(
    { _id: { $in: taskIds } },
    { $set: { mostRecentSyncToVectorDatabase: now } }
  );

  console.log(`Updated sync timestamp for ${taskIds.length} tasks`);
}

/**
 * Main sync function
 */
export async function syncTasksToPinecone() {
  console.log("Starting task sync to Pinecone...");

  try {
    // Connect to database
    await connectMongo();
    console.log("Connected to MongoDB");

    // Ensure models are registered
    ensureModelsRegistered();

    // Ensure Pinecone index exists
    await ensureIndexExists();

    // Get tasks that need syncing
    const tasks = await getTasksToSync();
    console.log(`Found ${tasks.length} tasks to sync`);

    if (tasks.length === 0) {
      console.log("No tasks to sync. Exiting.");
      return;
    }

    // Convert tasks to Pinecone records using batched embeddings
    console.log(
      "Generating embeddings in batches and converting to Pinecone records..."
    );
    const records = await tasksToRecordsBatch(tasks);

    if (records.length === 0) {
      console.log("No records to upsert. Exiting.");
      return;
    }

    // Upsert to Pinecone
    console.log(`\nUpserting ${records.length} records to Pinecone...`);
    await upsertRecordsToPinecone(records);

    // Update task sync timestamps
    const taskIds = records.map((record) => record.metadata.taskId);
    await updateTaskSyncTimestamp(taskIds);

    console.log("\n✅ Task sync completed successfully!");
    console.log(`Total tasks synced: ${records.length}`);
  } catch (error) {
    console.error("❌ Task sync failed:", error);
    throw error;
  }
}

/**
 * Convert multiple tasks to Pinecone records using batched embedding generation
 */
async function tasksToRecordsBatch(tasks: ITask[]): Promise<PineconeRecord[]> {
  // Prepare texts for batched embedding - include task name, description, and role names
  const taskTexts = tasks.map((task) => {
    const roles = task.roles as unknown as IRole[];
    const roleNames = roles.map((role) => role.roleName).join(", ");
    return `${task.taskName} ${task.taskDescription} Roles: ${roleNames}`;
  });

  console.log(`Generating embeddings for ${taskTexts.length} tasks...`);
  const embeddings = await generateEmbeddingsBatch(taskTexts);

  if (embeddings.length !== tasks.length) {
    throw new Error(
      `Embedding count (${embeddings.length}) doesn't match task count (${tasks.length})`
    );
  }

  // Build records with the generated embeddings
  const records: PineconeRecord[] = [];
  const now = Date.now();

  for (let i = 0; i < tasks.length; i++) {
    const task = tasks[i];
    const embedding = embeddings[i];

    // Ensure both task and embedding exist
    if (!task || !embedding) {
      console.error(`✗ Missing task or embedding at index ${i}`);
      continue;
    }

    try {
      // Get role information - task.roles is populated so it contains IRole objects
      const roles = task.roles as unknown as IRole[];
      const roleIds = roles.map((role) => role._id.toString());
      const roleNames = roles.map((role) => role.roleName);

      const metadata = createPineconeMetadata({
        taskId: task._id.toString(),
        taskName: task.taskName,
        taskDescription: task.taskDescription,
        roleIds,
        roleNames,
        lastSyncedAt: now,
        taskUpdatedAt: task.updatedAt.getTime(),
        taskCreatedAt: task.createdAt.getTime(),
      });

      const record = createPineconeRecord({
        id: `task_${task._id.toString()}`,
        values: embedding,
        metadata,
      });

      records.push(record);

      console.log(`✓ Processed task: ${task.taskName}`);
    } catch (error) {
      console.error(`✗ Failed to process task ${task._id}:`, error);
    }
  }

  return records;
}

// If this script is run directly
if (require.main === module) {
  syncTasksToPinecone()
    .then(() => {
      console.log("Script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

// Ensure models are registered (this is important for standalone scripts)
const ensureModelsRegistered = () => {
  // Import statements above already register the models, but we can double-check
  if (!Role || !Task || !Solution) {
    throw new Error("Models not properly imported");
  }

  // Debug: Log what models are registered
  const registeredModels = Object.keys(require("mongoose").models);
  console.log("✓ Registered models:", registeredModels);

  // Verify our models are in the registered list
  if (!registeredModels.includes("Role")) {
    throw new Error("Role model not registered with Mongoose");
  }
  if (!registeredModels.includes("Task")) {
    throw new Error("Task model not registered with Mongoose");
  }
  if (!registeredModels.includes("Solution")) {
    throw new Error("Solution model not registered with Mongoose");
  }

  console.log("✓ Models registered successfully");
};
